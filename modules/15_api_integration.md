# API & Integration Layer

## Overview
This module provides a comprehensive API layer and integration capabilities for rInvoice, enabling third-party integrations, webhook support, and external service connections. It includes RESTful APIs, webhook management, rate limiting, API documentation, and integration with popular business tools and services.

## Core Functionalities
- RESTful API endpoints for all business operations
- API authentication and authorization
- Rate limiting and throttling
- Webhook management and delivery
- Third-party service integrations (accounting, CRM, etc.)
- API documentation and developer portal
- SDK generation for popular languages
- Real-time API monitoring and analytics
- API versioning and backward compatibility
- Bulk operations and batch processing

## Technical Specifications

### Database Schema
```sql
-- API keys for external access
CREATE TABLE app.api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    key_name TEXT NOT NULL,
    key_hash TEXT NOT NULL UNIQUE, -- Hashed API key
    key_prefix TEXT NOT NULL, -- First 8 characters for identification
    
    -- Permissions and scope
    scopes TEXT[] NOT NULL DEFAULT ARRAY['read'], -- read, write, admin
    allowed_ips INET[], -- IP whitelist
    
    -- Usage limits
    rate_limit_per_minute INTEGER DEFAULT 60,
    rate_limit_per_hour INTEGER DEFAULT 1000,
    rate_limit_per_day INTEGER DEFAULT 10000,
    
    -- Status and metadata
    is_active BOOLEAN DEFAULT TRUE,
    last_used_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- API usage tracking
CREATE TABLE app.api_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    api_key_id UUID NOT NULL REFERENCES app.api_keys(id) ON DELETE CASCADE,
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    -- Request details
    endpoint TEXT NOT NULL,
    method TEXT NOT NULL,
    status_code INTEGER NOT NULL,
    response_time_ms INTEGER,
    
    -- Request metadata
    ip_address INET,
    user_agent TEXT,
    request_size INTEGER,
    response_size INTEGER,
    
    -- Timestamps
    requested_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Partitioning helper
    requested_date DATE NOT NULL DEFAULT CURRENT_DATE
) PARTITION BY RANGE (requested_date);

-- Create partitions for API usage (daily partitions)
CREATE TABLE app.api_usage_2024_01 PARTITION OF app.api_usage
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- Webhook endpoints
CREATE TABLE app.webhook_endpoints (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    url TEXT NOT NULL,
    secret TEXT NOT NULL, -- For signature verification
    
    -- Event subscriptions
    events TEXT[] NOT NULL, -- invoice.created, payment.received, etc.
    
    -- Configuration
    is_active BOOLEAN DEFAULT TRUE,
    retry_count INTEGER DEFAULT 3,
    timeout_seconds INTEGER DEFAULT 30,
    
    -- Status tracking
    last_success_at TIMESTAMPTZ,
    last_failure_at TIMESTAMPTZ,
    failure_count INTEGER DEFAULT 0,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Webhook deliveries
CREATE TABLE app.webhook_deliveries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    webhook_endpoint_id UUID NOT NULL REFERENCES app.webhook_endpoints(id) ON DELETE CASCADE,
    
    event_type TEXT NOT NULL,
    event_data JSONB NOT NULL,
    
    -- Delivery details
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'delivered', 'failed', 'retrying')),
    http_status_code INTEGER,
    response_body TEXT,
    response_headers JSONB,
    
    -- Timing
    scheduled_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    delivered_at TIMESTAMPTZ,
    next_retry_at TIMESTAMPTZ,
    retry_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Third-party integrations
CREATE TABLE app.integrations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    integration_type TEXT NOT NULL, -- 'quickbooks', 'xero', 'salesforce', etc.
    integration_name TEXT NOT NULL,
    
    -- Configuration
    configuration JSONB NOT NULL, -- API keys, settings, etc.
    field_mappings JSONB, -- Field mapping configuration
    
    -- Sync settings
    auto_sync BOOLEAN DEFAULT FALSE,
    sync_frequency TEXT DEFAULT 'manual', -- manual, hourly, daily, weekly
    last_sync_at TIMESTAMPTZ,
    next_sync_at TIMESTAMPTZ,
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    status TEXT NOT NULL DEFAULT 'connected' CHECK (status IN ('connected', 'error', 'disconnected')),
    error_message TEXT,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, integration_type)
);

-- Integration sync logs
CREATE TABLE app.integration_sync_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    integration_id UUID NOT NULL REFERENCES app.integrations(id) ON DELETE CASCADE,
    
    sync_type TEXT NOT NULL, -- 'full', 'incremental', 'manual'
    direction TEXT NOT NULL CHECK (direction IN ('import', 'export', 'bidirectional')),
    
    -- Results
    status TEXT NOT NULL CHECK (status IN ('running', 'completed', 'failed', 'cancelled')),
    records_processed INTEGER DEFAULT 0,
    records_success INTEGER DEFAULT 0,
    records_failed INTEGER DEFAULT 0,
    
    -- Details
    error_details JSONB,
    sync_summary JSONB,
    
    started_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    
    created_by UUID REFERENCES app.users(id)
);

-- Rate limiting tracking
CREATE TABLE app.rate_limits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    api_key_id UUID REFERENCES app.api_keys(id) ON DELETE CASCADE,
    ip_address INET,
    
    -- Time windows
    window_start TIMESTAMPTZ NOT NULL,
    window_end TIMESTAMPTZ NOT NULL,
    
    -- Counters
    request_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(api_key_id, window_start) WHERE api_key_id IS NOT NULL,
    UNIQUE(ip_address, window_start) WHERE ip_address IS NOT NULL
);

-- Indexes for performance
CREATE INDEX idx_api_keys_business_id ON app.api_keys(business_id);
CREATE INDEX idx_api_keys_hash ON app.api_keys(key_hash);
CREATE INDEX idx_api_keys_prefix ON app.api_keys(key_prefix);
CREATE INDEX idx_api_usage_api_key_id ON app.api_usage(api_key_id);
CREATE INDEX idx_api_usage_requested_at ON app.api_usage(requested_at);
CREATE INDEX idx_webhook_endpoints_business_id ON app.webhook_endpoints(business_id);
CREATE INDEX idx_webhook_deliveries_endpoint_id ON app.webhook_deliveries(webhook_endpoint_id);
CREATE INDEX idx_webhook_deliveries_status ON app.webhook_deliveries(status);
CREATE INDEX idx_webhook_deliveries_scheduled_at ON app.webhook_deliveries(scheduled_at);
CREATE INDEX idx_integrations_business_id ON app.integrations(business_id);
CREATE INDEX idx_integration_sync_logs_integration_id ON app.integration_sync_logs(integration_id);
CREATE INDEX idx_rate_limits_api_key_window ON app.rate_limits(api_key_id, window_start);
CREATE INDEX idx_rate_limits_ip_window ON app.rate_limits(ip_address, window_start);

-- Enable RLS
ALTER TABLE app.api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.api_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.webhook_endpoints ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.webhook_deliveries ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.integration_sync_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.rate_limits ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Business admins can manage API keys"
ON app.api_keys FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() 
        AND role IN ('owner', 'admin')
        AND is_active = TRUE
    )
);

CREATE POLICY "Business members can view API usage"
ON app.api_usage FOR SELECT
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);

CREATE POLICY "Business admins can manage webhooks"
ON app.webhook_endpoints FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() 
        AND role IN ('owner', 'admin')
        AND is_active = TRUE
    )
);

CREATE POLICY "Business admins can manage integrations"
ON app.integrations FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() 
        AND role IN ('owner', 'admin')
        AND is_active = TRUE
    )
);
```

### API Service
```typescript
// lib/api.ts
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import crypto from 'crypto';

export interface ApiKey {
  id: string;
  business_id: string;
  key_name: string;
  key_prefix: string;
  scopes: string[];
  allowed_ips?: string[];
  rate_limit_per_minute: number;
  rate_limit_per_hour: number;
  rate_limit_per_day: number;
  is_active: boolean;
  last_used_at?: string;
  expires_at?: string;
  created_at: string;
}

export interface WebhookEndpoint {
  id: string;
  business_id: string;
  url: string;
  events: string[];
  is_active: boolean;
  retry_count: number;
  timeout_seconds: number;
  last_success_at?: string;
  last_failure_at?: string;
  failure_count: number;
}

export interface Integration {
  id: string;
  business_id: string;
  integration_type: string;
  integration_name: string;
  configuration: any;
  field_mappings?: any;
  auto_sync: boolean;
  sync_frequency: string;
  last_sync_at?: string;
  next_sync_at?: string;
  is_active: boolean;
  status: 'connected' | 'error' | 'disconnected';
  error_message?: string;
}

export class ApiService {
  private supabase = createClientComponentClient();

  async createApiKey(businessId: string, keyData: {
    key_name: string;
    scopes: string[];
    allowed_ips?: string[];
    rate_limit_per_minute?: number;
    rate_limit_per_hour?: number;
    rate_limit_per_day?: number;
    expires_at?: string;
  }): Promise<{ apiKey: ApiKey; plainKey: string }> {
    // Generate API key
    const plainKey = this.generateApiKey();
    const keyHash = this.hashApiKey(plainKey);
    const keyPrefix = plainKey.substring(0, 8);

    const { data, error } = await this.supabase
      .from('api_keys')
      .insert({
        business_id: businessId,
        key_name: keyData.key_name,
        key_hash: keyHash,
        key_prefix: keyPrefix,
        scopes: keyData.scopes,
        allowed_ips: keyData.allowed_ips,
        rate_limit_per_minute: keyData.rate_limit_per_minute || 60,
        rate_limit_per_hour: keyData.rate_limit_per_hour || 1000,
        rate_limit_per_day: keyData.rate_limit_per_day || 10000,
        expires_at: keyData.expires_at,
        created_by: (await this.supabase.auth.getUser()).data.user?.id,
      })
      .select()
      .single();

    if (error) throw error;

    return {
      apiKey: data,
      plainKey,
    };
  }

  async validateApiKey(apiKey: string): Promise<ApiKey | null> {
    const keyHash = this.hashApiKey(apiKey);
    
    const { data, error } = await this.supabase
      .from('api_keys')
      .select('*')
      .eq('key_hash', keyHash)
      .eq('is_active', true)
      .single();

    if (error || !data) return null;

    // Check expiry
    if (data.expires_at && new Date(data.expires_at) < new Date()) {
      return null;
    }

    // Update last used timestamp
    await this.supabase
      .from('api_keys')
      .update({ last_used_at: new Date().toISOString() })
      .eq('id', data.id);

    return data;
  }

  async checkRateLimit(apiKeyId: string, ipAddress?: string): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: Date;
  }> {
    const now = new Date();
    const windowStart = new Date(now.getTime() - (60 * 1000)); // 1 minute window
    const windowEnd = now;

    // Get current usage in the window
    const { data: usage } = await this.supabase
      .from('rate_limits')
      .select('request_count')
      .eq('api_key_id', apiKeyId)
      .eq('window_start', windowStart.toISOString())
      .single();

    const currentCount = usage?.request_count || 0;

    // Get API key limits
    const { data: apiKey } = await this.supabase
      .from('api_keys')
      .select('rate_limit_per_minute')
      .eq('id', apiKeyId)
      .single();

    const limit = apiKey?.rate_limit_per_minute || 60;
    const allowed = currentCount < limit;

    if (allowed) {
      // Increment counter
      await this.supabase
        .from('rate_limits')
        .upsert({
          api_key_id: apiKeyId,
          window_start: windowStart.toISOString(),
          window_end: windowEnd.toISOString(),
          request_count: currentCount + 1,
        });
    }

    return {
      allowed,
      remaining: Math.max(0, limit - currentCount - 1),
      resetTime: new Date(windowStart.getTime() + (60 * 1000)),
    };
  }

  async logApiUsage(apiKeyId: string, requestData: {
    endpoint: string;
    method: string;
    status_code: number;
    response_time_ms: number;
    ip_address?: string;
    user_agent?: string;
    request_size?: number;
    response_size?: number;
  }): Promise<void> {
    const { data: apiKey } = await this.supabase
      .from('api_keys')
      .select('business_id')
      .eq('id', apiKeyId)
      .single();

    if (!apiKey) return;

    await this.supabase
      .from('api_usage')
      .insert({
        api_key_id: apiKeyId,
        business_id: apiKey.business_id,
        ...requestData,
      });
  }

  async createWebhookEndpoint(businessId: string, webhookData: {
    url: string;
    events: string[];
    retry_count?: number;
    timeout_seconds?: number;
  }): Promise<WebhookEndpoint> {
    const secret = this.generateWebhookSecret();

    const { data, error } = await this.supabase
      .from('webhook_endpoints')
      .insert({
        business_id: businessId,
        url: webhookData.url,
        secret,
        events: webhookData.events,
        retry_count: webhookData.retry_count || 3,
        timeout_seconds: webhookData.timeout_seconds || 30,
        created_by: (await this.supabase.auth.getUser()).data.user?.id,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async deliverWebhook(businessId: string, eventType: string, eventData: any): Promise<void> {
    // Get all active webhook endpoints for this business and event type
    const { data: endpoints } = await this.supabase
      .from('webhook_endpoints')
      .select('*')
      .eq('business_id', businessId)
      .eq('is_active', true)
      .contains('events', [eventType]);

    if (!endpoints || endpoints.length === 0) return;

    // Create delivery records for each endpoint
    for (const endpoint of endpoints) {
      await this.supabase
        .from('webhook_deliveries')
        .insert({
          webhook_endpoint_id: endpoint.id,
          event_type: eventType,
          event_data: eventData,
        });
    }

    // Process deliveries (this would typically be done by a background job)
    await this.processWebhookDeliveries();
  }

  async createIntegration(businessId: string, integrationData: {
    integration_type: string;
    integration_name: string;
    configuration: any;
    field_mappings?: any;
    auto_sync?: boolean;
    sync_frequency?: string;
  }): Promise<Integration> {
    const { data, error } = await this.supabase
      .from('integrations')
      .insert({
        business_id: businessId,
        ...integrationData,
        created_by: (await this.supabase.auth.getUser()).data.user?.id,
      })
      .select()
      .single();

    if (error) throw error;

    // Test the integration connection
    await this.testIntegrationConnection(data.id);

    return data;
  }

  async syncIntegration(integrationId: string, syncType: 'full' | 'incremental' | 'manual' = 'manual'): Promise<void> {
    const { data: integration } = await this.supabase
      .from('integrations')
      .select('*')
      .eq('id', integrationId)
      .single();

    if (!integration) throw new Error('Integration not found');

    // Create sync log
    const { data: syncLog } = await this.supabase
      .from('integration_sync_logs')
      .insert({
        integration_id: integrationId,
        sync_type: syncType,
        direction: 'bidirectional',
        status: 'running',
        started_at: new Date().toISOString(),
        created_by: (await this.supabase.auth.getUser()).data.user?.id,
      })
      .select()
      .single();

    try {
      // Perform the actual sync based on integration type
      const result = await this.performIntegrationSync(integration, syncType);

      // Update sync log with results
      await this.supabase
        .from('integration_sync_logs')
        .update({
          status: 'completed',
          records_processed: result.processed,
          records_success: result.success,
          records_failed: result.failed,
          sync_summary: result.summary,
          completed_at: new Date().toISOString(),
        })
        .eq('id', syncLog.id);

      // Update integration last sync time
      await this.supabase
        .from('integrations')
        .update({
          last_sync_at: new Date().toISOString(),
          status: 'connected',
          error_message: null,
        })
        .eq('id', integrationId);

    } catch (error) {
      // Update sync log with error
      await this.supabase
        .from('integration_sync_logs')
        .update({
          status: 'failed',
          error_details: { message: error.message, stack: error.stack },
          completed_at: new Date().toISOString(),
        })
        .eq('id', syncLog.id);

      // Update integration status
      await this.supabase
        .from('integrations')
        .update({
          status: 'error',
          error_message: error.message,
        })
        .eq('id', integrationId);

      throw error;
    }
  }

  async getApiAnalytics(businessId: string, dateRange: {
    start: string;
    end: string;
  }): Promise<{
    totalRequests: number;
    successRate: number;
    averageResponseTime: number;
    topEndpoints: Array<{ endpoint: string; count: number }>;
    requestsByDay: Array<{ date: string; count: number }>;
  }> {
    const { data: usage } = await this.supabase
      .from('api_usage')
      .select('*')
      .eq('business_id', businessId)
      .gte('requested_at', dateRange.start)
      .lte('requested_at', dateRange.end);

    if (!usage || usage.length === 0) {
      return {
        totalRequests: 0,
        successRate: 0,
        averageResponseTime: 0,
        topEndpoints: [],
        requestsByDay: [],
      };
    }

    const totalRequests = usage.length;
    const successfulRequests = usage.filter(u => u.status_code >= 200 && u.status_code < 400).length;
    const successRate = (successfulRequests / totalRequests) * 100;
    const averageResponseTime = usage.reduce((sum, u) => sum + u.response_time_ms, 0) / totalRequests;

    // Top endpoints
    const endpointCounts = usage.reduce((acc, u) => {
      acc[u.endpoint] = (acc[u.endpoint] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const topEndpoints = Object.entries(endpointCounts)
      .map(([endpoint, count]) => ({ endpoint, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Requests by day
    const dailyCounts = usage.reduce((acc, u) => {
      const date = u.requested_at.split('T')[0];
      acc[date] = (acc[date] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const requestsByDay = Object.entries(dailyCounts)
      .map(([date, count]) => ({ date, count }))
      .sort((a, b) => a.date.localeCompare(b.date));

    return {
      totalRequests,
      successRate,
      averageResponseTime,
      topEndpoints,
      requestsByDay,
    };
  }

  private generateApiKey(): string {
    return 'riv_' + crypto.randomBytes(32).toString('hex');
  }

  private hashApiKey(apiKey: string): string {
    return crypto.createHash('sha256').update(apiKey).digest('hex');
  }

  private generateWebhookSecret(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  private async processWebhookDeliveries(): Promise<void> {
    // This would typically be handled by a background job
    // Get pending deliveries and attempt to deliver them
    const { data: deliveries } = await this.supabase
      .from('webhook_deliveries')
      .select(`
        *,
        webhook_endpoint:webhook_endpoints(*)
      `)
      .eq('status', 'pending')
      .lte('scheduled_at', new Date().toISOString())
      .limit(100);

    if (!deliveries) return;

    for (const delivery of deliveries) {
      await this.attemptWebhookDelivery(delivery);
    }
  }

  private async attemptWebhookDelivery(delivery: any): Promise<void> {
    try {
      const signature = this.generateWebhookSignature(
        delivery.event_data,
        delivery.webhook_endpoint.secret
      );

      const response = await fetch(delivery.webhook_endpoint.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Webhook-Signature': signature,
          'X-Webhook-Event': delivery.event_type,
        },
        body: JSON.stringify(delivery.event_data),
        signal: AbortSignal.timeout(delivery.webhook_endpoint.timeout_seconds * 1000),
      });

      await this.supabase
        .from('webhook_deliveries')
        .update({
          status: response.ok ? 'delivered' : 'failed',
          http_status_code: response.status,
          response_body: await response.text(),
          delivered_at: new Date().toISOString(),
        })
        .eq('id', delivery.id);

      // Update endpoint success/failure tracking
      if (response.ok) {
        await this.supabase
          .from('webhook_endpoints')
          .update({
            last_success_at: new Date().toISOString(),
            failure_count: 0,
          })
          .eq('id', delivery.webhook_endpoint_id);
      } else {
        await this.supabase
          .from('webhook_endpoints')
          .update({
            last_failure_at: new Date().toISOString(),
            failure_count: delivery.webhook_endpoint.failure_count + 1,
          })
          .eq('id', delivery.webhook_endpoint_id);
      }

    } catch (error) {
      // Handle delivery failure and schedule retry if applicable
      const newRetryCount = delivery.retry_count + 1;
      const maxRetries = delivery.webhook_endpoint.retry_count;

      if (newRetryCount <= maxRetries) {
        const nextRetryAt = new Date(Date.now() + Math.pow(2, newRetryCount) * 60000); // Exponential backoff

        await this.supabase
          .from('webhook_deliveries')
          .update({
            status: 'retrying',
            retry_count: newRetryCount,
            next_retry_at: nextRetryAt.toISOString(),
          })
          .eq('id', delivery.id);
      } else {
        await this.supabase
          .from('webhook_deliveries')
          .update({
            status: 'failed',
            retry_count: newRetryCount,
          })
          .eq('id', delivery.id);
      }
    }
  }

  private generateWebhookSignature(payload: any, secret: string): string {
    const payloadString = JSON.stringify(payload);
    return crypto.createHmac('sha256', secret).update(payloadString).digest('hex');
  }

  private async testIntegrationConnection(integrationId: string): Promise<void> {
    // Test the integration connection based on type
    // This would be implemented for each specific integration
  }

  private async performIntegrationSync(integration: Integration, syncType: string): Promise<{
    processed: number;
    success: number;
    failed: number;
    summary: any;
  }> {
    // Perform the actual sync based on integration type
    // This would be implemented for each specific integration
    return {
      processed: 0,
      success: 0,
      failed: 0,
      summary: {},
    };
  }
}
```

## Security Measures
- **API Key Security**: Secure key generation, hashing, and validation
- **Rate Limiting**: Comprehensive rate limiting per API key and IP
- **Webhook Security**: Signature verification for webhook deliveries
- **Scope-based Access**: Granular permissions for API operations
- **IP Whitelisting**: Optional IP-based access control
- **Audit Logging**: Complete API usage tracking and monitoring

## Integration Points
- **All Business Modules**: API endpoints for all business operations
- **Authentication & Authorization**: API key validation and permissions
- **Audit Logging**: API usage and security event logging
- **Webhook System**: Event-driven integrations
- **Third-party Services**: External service integrations

## Error Handling & Validation
- **API Validation**: Comprehensive input validation for all endpoints
- **Rate Limit Handling**: Graceful rate limit responses
- **Webhook Retry Logic**: Exponential backoff for failed deliveries
- **Integration Error Handling**: Robust error handling for external services
- **API Versioning**: Backward compatibility and deprecation handling

## Testing Strategy
- **Unit Tests**: API service methods and validation logic
- **Integration Tests**: API endpoints and webhook delivery
- **Load Tests**: API performance under high load
- **Security Tests**: API key security and rate limiting
- **E2E Tests**: Complete integration workflows

## Implementation Tasks
1. **API Infrastructure Setup**
   - Create API key management system
   - Implement rate limiting middleware
   - Set up API documentation and developer portal

2. **RESTful API Development**
   - Build comprehensive API endpoints
   - Implement API versioning
   - Create API response standards

3. **Webhook System**
   - Build webhook endpoint management
   - Implement webhook delivery system
   - Create webhook retry and failure handling

4. **Third-party Integrations**
   - Build integration framework
   - Implement specific integrations (QuickBooks, Xero, etc.)
   - Create sync and mapping systems

5. **API Monitoring**
   - Build API analytics dashboard
   - Implement real-time monitoring
   - Create API usage reporting

6. **Developer Experience**
   - Create API documentation
   - Build SDK generation
   - Implement developer portal

## Dependencies
- **All Business Modules**: Source of API endpoints and data
- **Authentication & Authorization**: API key validation
- **Audit Logging**: API usage tracking
- **Background Jobs**: Webhook delivery and integration sync
- **Email Service**: API key and integration notifications

## Success Criteria
- ✅ Comprehensive API coverage for all business operations
- ✅ API key management and security works properly
- ✅ Rate limiting prevents abuse and ensures fair usage
- ✅ Webhook delivery is reliable with proper retry logic
- ✅ Third-party integrations sync data accurately
- ✅ API documentation is comprehensive and up-to-date
- ✅ API performance meets SLA requirements
- ✅ Developer experience is smooth and well-documented
