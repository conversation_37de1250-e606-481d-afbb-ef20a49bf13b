# API & Integration Layer

## Overview
This module provides a comprehensive API layer and integration capabilities for rInvoice, enabling third-party integrations, webhook support, and external service connections. It includes RESTful APIs, webhook management, rate limiting, API documentation, and integration with popular business tools and services.

## Core Functionalities
- RESTful API endpoints for all business operations
- API authentication and authorization
- Rate limiting and throttling
- Webhook management and delivery
- Third-party service integrations (accounting, CRM, etc.)
- API documentation and developer portal
- SDK generation for popular languages
- Real-time API monitoring and analytics
- API versioning and backward compatibility
- Bulk operations and batch processing

## Technical Specifications

### Database Schema
```sql
-- API keys for external access
CREATE TABLE app.api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    key_name TEXT NOT NULL,
    key_hash TEXT NOT NULL UNIQUE, -- Hashed API key
    key_prefix TEXT NOT NULL, -- First 8 characters for identification
    
    -- Permissions and scope
    scopes TEXT[] NOT NULL DEFAULT ARRAY['read'], -- read, write, admin
    allowed_ips INET[], -- IP whitelist
    
    -- Usage limits
    rate_limit_per_minute INTEGER DEFAULT 60,
    rate_limit_per_hour INTEGER DEFAULT 1000,
    rate_limit_per_day INTEGER DEFAULT 10000,
    
    -- Status and metadata
    is_active BOOLEAN DEFAULT TRUE,
    last_used_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- API usage tracking
CREATE TABLE app.api_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    api_key_id UUID NOT NULL REFERENCES app.api_keys(id) ON DELETE CASCADE,
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    -- Request details
    endpoint TEXT NOT NULL,
    method TEXT NOT NULL,
    status_code INTEGER NOT NULL,
    response_time_ms INTEGER,
    
    -- Request metadata
    ip_address INET,
    user_agent TEXT,
    request_size INTEGER,
    response_size INTEGER,
    
    -- Timestamps
    requested_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Partitioning helper
    requested_date DATE NOT NULL DEFAULT CURRENT_DATE
) PARTITION BY RANGE (requested_date);

-- Create partitions for API usage (daily partitions)
CREATE TABLE app.api_usage_2024_01 PARTITION OF app.api_usage
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- Webhook endpoints
CREATE TABLE app.webhook_endpoints (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    url TEXT NOT NULL,
    secret TEXT NOT NULL, -- For signature verification
    
    -- Event subscriptions
    events TEXT[] NOT NULL, -- invoice.created, payment.received, etc.
    
    -- Configuration
    is_active BOOLEAN DEFAULT TRUE,
    retry_count INTEGER DEFAULT 3,
    timeout_seconds INTEGER DEFAULT 30,
    
    -- Status tracking
    last_success_at TIMESTAMPTZ,
    last_failure_at TIMESTAMPTZ,
    failure_count INTEGER DEFAULT 0,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Webhook deliveries
CREATE TABLE app.webhook_deliveries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    webhook_endpoint_id UUID NOT NULL REFERENCES app.webhook_endpoints(id) ON DELETE CASCADE,
    
    event_type TEXT NOT NULL,
    event_data JSONB NOT NULL,
    
    -- Delivery details
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'delivered', 'failed', 'retrying')),
    http_status_code INTEGER,
    response_body TEXT,
    response_headers JSONB,
    
    -- Timing
    scheduled_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    delivered_at TIMESTAMPTZ,
    next_retry_at TIMESTAMPTZ,
    retry_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Third-party integrations
CREATE TABLE app.integrations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    integration_type TEXT NOT NULL, -- 'quickbooks', 'xero', 'salesforce', etc.
    integration_name TEXT NOT NULL,
    
    -- Configuration
    configuration JSONB NOT NULL, -- API keys, settings, etc.
    field_mappings JSONB, -- Field mapping configuration
    
    -- Sync settings
    auto_sync BOOLEAN DEFAULT FALSE,
    sync_frequency TEXT DEFAULT 'manual', -- manual, hourly, daily, weekly
    last_sync_at TIMESTAMPTZ,
    next_sync_at TIMESTAMPTZ,
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    status TEXT NOT NULL DEFAULT 'connected' CHECK (status IN ('connected', 'error', 'disconnected')),
    error_message TEXT,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, integration_type)
);

-- Integration sync logs
CREATE TABLE app.integration_sync_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    integration_id UUID NOT NULL REFERENCES app.integrations(id) ON DELETE CASCADE,
    
    sync_type TEXT NOT NULL, -- 'full', 'incremental', 'manual'
    direction TEXT NOT NULL CHECK (direction IN ('import', 'export', 'bidirectional')),
    
    -- Results
    status TEXT NOT NULL CHECK (status IN ('running', 'completed', 'failed', 'cancelled')),
    records_processed INTEGER DEFAULT 0,
    records_success INTEGER DEFAULT 0,
    records_failed INTEGER DEFAULT 0,
    
    -- Details
    error_details JSONB,
    sync_summary JSONB,
    
    started_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    
    created_by UUID REFERENCES app.users(id)
);

-- Rate limiting tracking
CREATE TABLE app.rate_limits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    api_key_id UUID REFERENCES app.api_keys(id) ON DELETE CASCADE,
    ip_address INET,
    
    -- Time windows
    window_start TIMESTAMPTZ NOT NULL,
    window_end TIMESTAMPTZ NOT NULL,
    
    -- Counters
    request_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(api_key_id, window_start) WHERE api_key_id IS NOT NULL,
    UNIQUE(ip_address, window_start) WHERE ip_address IS NOT NULL
);

-- Indexes for performance
CREATE INDEX idx_api_keys_business_id ON app.api_keys(business_id);
CREATE INDEX idx_api_keys_hash ON app.api_keys(key_hash);
CREATE INDEX idx_api_keys_prefix ON app.api_keys(key_prefix);
CREATE INDEX idx_api_usage_api_key_id ON app.api_usage(api_key_id);
CREATE INDEX idx_api_usage_requested_at ON app.api_usage(requested_at);
CREATE INDEX idx_webhook_endpoints_business_id ON app.webhook_endpoints(business_id);
CREATE INDEX idx_webhook_deliveries_endpoint_id ON app.webhook_deliveries(webhook_endpoint_id);
CREATE INDEX idx_webhook_deliveries_status ON app.webhook_deliveries(status);
CREATE INDEX idx_webhook_deliveries_scheduled_at ON app.webhook_deliveries(scheduled_at);
CREATE INDEX idx_integrations_business_id ON app.integrations(business_id);
CREATE INDEX idx_integration_sync_logs_integration_id ON app.integration_sync_logs(integration_id);
CREATE INDEX idx_rate_limits_api_key_window ON app.rate_limits(api_key_id, window_start);
CREATE INDEX idx_rate_limits_ip_window ON app.rate_limits(ip_address, window_start);

-- Enable RLS
ALTER TABLE app.api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.api_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.webhook_endpoints ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.webhook_deliveries ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.integration_sync_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.rate_limits ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Business admins can manage API keys"
ON app.api_keys FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() 
        AND role IN ('owner', 'admin')
        AND is_active = TRUE
    )
);

CREATE POLICY "Business members can view API usage"
ON app.api_usage FOR SELECT
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);

CREATE POLICY "Business admins can manage webhooks"
ON app.webhook_endpoints FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() 
        AND role IN ('owner', 'admin')
        AND is_active = TRUE
    )
);

CREATE POLICY "Business admins can manage integrations"
ON app.integrations FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() 
        AND role IN ('owner', 'admin')
        AND is_active = TRUE
    )
);
```

### API Service Architecture

**Core Service: APIService**
- **Primary Responsibilities**: External API management, webhook handling, integration orchestration, rate limiting
- **Key Methods**:
  - `createIntegration(businessId, integrationData)` - Third-party service integration setup
  - `updateIntegration(integrationId, updates)` - Integration configuration management
  - `testConnection(integrationId)` - Integration connectivity testing
  - `syncData(integrationId, syncType)` - Data synchronization with external services
  - `handleWebhook(source, payload, signature)` - Incoming webhook processing
  - `sendWebhook(targetUrl, payload, retryConfig)` - Outgoing webhook delivery
  - `getAPIUsage(businessId, timeframe)` - API usage analytics and monitoring
  - `manageRateLimit(apiKey, endpoint)` - Rate limiting and throttling management

**Implementation Approach**:
- Build comprehensive integration framework supporting multiple third-party services
- Implement robust webhook system with signature verification and retry logic
- Create API rate limiting and usage monitoring system
- Establish data synchronization engine with conflict resolution
- Build integration marketplace with pre-built connectors

### Integration Strategy

**Third-Party Integrations**:
- **Payment Processors**: Stripe, PayPal, Square, and other payment gateways
- **Accounting Software**: QuickBooks, Xero, FreshBooks integration
- **CRM Systems**: Salesforce, HubSpot, Pipedrive connectivity
- **Email Services**: SendGrid, Mailchimp, Constant Contact integration
- **Cloud Storage**: Google Drive, Dropbox, OneDrive file sync
- **Communication**: Slack, Microsoft Teams, Discord notifications

**API Management**:
- **RESTful APIs**: Comprehensive REST API with OpenAPI documentation
- **GraphQL Support**: Flexible GraphQL endpoint for complex queries
- **Authentication**: OAuth 2.0, API keys, and JWT token management
- **Rate Limiting**: Intelligent rate limiting with burst capacity
- **Versioning**: API versioning with backward compatibility
- **Monitoring**: Real-time API performance and usage analytics

**Webhook System**:
- **Incoming Webhooks**: Secure webhook endpoints with signature verification
- **Outgoing Webhooks**: Reliable webhook delivery with retry mechanisms
- **Event Routing**: Intelligent event routing and filtering
- **Payload Transformation**: Data transformation and mapping capabilities
- **Delivery Guarantees**: At-least-once delivery with idempotency support





## Security Measures
- **API Key Security**: Secure key generation, hashing, and validation
- **Rate Limiting**: Comprehensive rate limiting per API key and IP
- **Webhook Security**: Signature verification for webhook deliveries
- **Scope-based Access**: Granular permissions for API operations
- **IP Whitelisting**: Optional IP-based access control
- **Audit Logging**: Complete API usage tracking and monitoring

## Integration Points
- **All Business Modules**: API endpoints for all business operations
- **Authentication & Authorization**: API key validation and permissions
- **Audit Logging**: API usage and security event logging
- **Webhook System**: Event-driven integrations
- **Third-party Services**: External service integrations

## Error Handling & Validation
- **API Validation**: Comprehensive input validation for all endpoints
- **Rate Limit Handling**: Graceful rate limit responses
- **Webhook Retry Logic**: Exponential backoff for failed deliveries
- **Integration Error Handling**: Robust error handling for external services
- **API Versioning**: Backward compatibility and deprecation handling

## Testing Strategy
- **Unit Tests**: API service methods and validation logic
- **Integration Tests**: API endpoints and webhook delivery
- **Load Tests**: API performance under high load
- **Security Tests**: API key security and rate limiting
- **E2E Tests**: Complete integration workflows

## Implementation Tasks
1. **API Infrastructure Setup**
   - Create API key management system
   - Implement rate limiting middleware
   - Set up API documentation and developer portal

2. **RESTful API Development**
   - Build comprehensive API endpoints
   - Implement API versioning
   - Create API response standards

3. **Webhook System**
   - Build webhook endpoint management
   - Implement webhook delivery system
   - Create webhook retry and failure handling

4. **Third-party Integrations**
   - Build integration framework
   - Implement specific integrations (QuickBooks, Xero, etc.)
   - Create sync and mapping systems

5. **API Monitoring**
   - Build API analytics dashboard
   - Implement real-time monitoring
   - Create API usage reporting

6. **Developer Experience**
   - Create API documentation
   - Build SDK generation
   - Implement developer portal

## Dependencies
- **All Business Modules**: Source of API endpoints and data
- **Authentication & Authorization**: API key validation
- **Audit Logging**: API usage tracking
- **Background Jobs**: Webhook delivery and integration sync
- **Email Service**: API key and integration notifications

## Success Criteria
- ✅ Comprehensive API coverage for all business operations
- ✅ API key management and security works properly
- ✅ Rate limiting prevents abuse and ensures fair usage
- ✅ Webhook delivery is reliable with proper retry logic
- ✅ Third-party integrations sync data accurately
- ✅ API documentation is comprehensive and up-to-date
- ✅ API performance meets SLA requirements
- ✅ Developer experience is smooth and well-documented
