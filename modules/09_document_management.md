# Document Management & Templates

## Overview
This module handles all document-related functionality including PDF generation, template management, file storage, and document workflows. It provides comprehensive document management capabilities with support for custom templates, automated document generation, and secure file handling.

## Core Functionalities
- PDF generation for invoices, quotes, and reports
- Customizable document templates with variables
- File upload and storage management
- Document versioning and history
- Template editor with drag-and-drop interface
- Automated document generation workflows
- Document sharing and access control
- Digital signatures and document approval
- Bulk document operations
- Document analytics and tracking

## Technical Specifications

### Database Schema
```sql
-- Document templates
CREATE TABLE app.document_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    template_name TEXT NOT NULL,
    template_type TEXT NOT NULL, -- 'invoice', 'quote', 'receipt', 'report'
    description TEXT,
    
    -- Template content
    template_data JSONB NOT NULL, -- Template structure and styling
    variables JSONB, -- Available variables for this template
    
    -- Layout settings
    page_size TEXT DEFAULT 'A4',
    orientation TEXT DEFAULT 'portrait',
    margins JSONB DEFAULT '{"top": 20, "right": 20, "bottom": 20, "left": 20}',
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE,
    version INTEGER DEFAULT 1,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    updated_by UUID REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, template_name, template_type)
);

-- Generated documents
CREATE TABLE app.documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    template_id UUID REFERENCES app.document_templates(id),
    
    -- Document details
    document_name TEXT NOT NULL,
    document_type TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type TEXT NOT NULL DEFAULT 'application/pdf',
    
    -- Source reference
    source_type TEXT, -- 'invoice', 'quote', 'manual'
    source_id UUID,
    
    -- Generation details
    generation_data JSONB, -- Data used to generate the document
    generation_status TEXT DEFAULT 'completed' CHECK (generation_status IN ('pending', 'processing', 'completed', 'failed')),
    error_message TEXT,
    
    -- Access control
    is_public BOOLEAN DEFAULT FALSE,
    access_token TEXT, -- For public sharing
    expires_at TIMESTAMPTZ,
    
    -- Tracking
    download_count INTEGER DEFAULT 0,
    last_downloaded_at TIMESTAMPTZ,
    
    generated_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Document shares for controlled access
CREATE TABLE app.document_shares (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    document_id UUID NOT NULL REFERENCES app.documents(id) ON DELETE CASCADE,
    
    shared_with_email TEXT,
    shared_with_user_id UUID REFERENCES app.users(id),
    
    -- Permissions
    can_view BOOLEAN DEFAULT TRUE,
    can_download BOOLEAN DEFAULT TRUE,
    can_comment BOOLEAN DEFAULT FALSE,
    
    -- Access tracking
    access_count INTEGER DEFAULT 0,
    last_accessed_at TIMESTAMPTZ,
    
    -- Expiry
    expires_at TIMESTAMPTZ,
    
    shared_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- File storage metadata
CREATE TABLE app.file_storage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    file_name TEXT NOT NULL,
    original_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type TEXT NOT NULL,
    
    -- File categorization
    category TEXT, -- 'document', 'image', 'attachment'
    tags TEXT[],
    
    -- Storage details
    storage_provider TEXT DEFAULT 'supabase',
    storage_bucket TEXT,
    
    -- Access control
    is_public BOOLEAN DEFAULT FALSE,
    
    uploaded_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Document approval workflows
CREATE TABLE app.document_approvals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    document_id UUID NOT NULL REFERENCES app.documents(id) ON DELETE CASCADE,
    
    approver_id UUID NOT NULL REFERENCES app.users(id),
    approval_status TEXT NOT NULL DEFAULT 'pending' CHECK (approval_status IN ('pending', 'approved', 'rejected')),
    
    comments TEXT,
    approved_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_document_templates_business_type ON app.document_templates(business_id, template_type);
CREATE INDEX idx_documents_business_id ON app.documents(business_id);
CREATE INDEX idx_documents_source ON app.documents(source_type, source_id);
CREATE INDEX idx_documents_created_at ON app.documents(created_at);
CREATE INDEX idx_document_shares_document_id ON app.document_shares(document_id);
CREATE INDEX idx_file_storage_business_id ON app.file_storage(business_id);
CREATE INDEX idx_file_storage_category ON app.file_storage(category);
CREATE INDEX idx_document_approvals_document_id ON app.document_approvals(document_id);

-- Enable RLS
ALTER TABLE app.document_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.document_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.file_storage ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.document_approvals ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Business members can manage templates"
ON app.document_templates FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);

CREATE POLICY "Business members can view documents"
ON app.documents FOR SELECT
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
    OR is_public = TRUE
);

CREATE POLICY "Business members can manage documents"
ON app.documents FOR INSERT, UPDATE, DELETE
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);
```

### Document Service
```typescript
// lib/document.ts
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

export interface DocumentTemplate {
  id: string;
  business_id: string;
  template_name: string;
  template_type: string;
  description?: string;
  template_data: any;
  variables?: any;
  page_size: string;
  orientation: string;
  margins: any;
  is_active: boolean;
  is_default: boolean;
  version: number;
  created_at: string;
  updated_at: string;
}

export interface Document {
  id: string;
  business_id: string;
  template_id?: string;
  document_name: string;
  document_type: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  source_type?: string;
  source_id?: string;
  generation_data?: any;
  generation_status: string;
  error_message?: string;
  is_public: boolean;
  access_token?: string;
  expires_at?: string;
  download_count: number;
  last_downloaded_at?: string;
  created_at: string;
}

export interface FileStorage {
  id: string;
  business_id: string;
  file_name: string;
  original_name: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  category?: string;
  tags?: string[];
  storage_provider: string;
  storage_bucket?: string;
  is_public: boolean;
  created_at: string;
}

export class DocumentService {
  private supabase = createClientComponentClient();

  async createTemplate(businessId: string, templateData: Partial<DocumentTemplate>): Promise<DocumentTemplate> {
    const user = await this.supabase.auth.getUser();

    const { data, error } = await this.supabase
      .from('document_templates')
      .insert({
        business_id: businessId,
        ...templateData,
        created_by: user.data.user?.id,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async updateTemplate(templateId: string, updates: Partial<DocumentTemplate>): Promise<DocumentTemplate> {
    const user = await this.supabase.auth.getUser();

    const { data, error } = await this.supabase
      .from('document_templates')
      .update({
        ...updates,
        updated_by: user.data.user?.id,
        updated_at: new Date().toISOString(),
      })
      .eq('id', templateId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getTemplates(businessId: string, templateType?: string): Promise<DocumentTemplate[]> {
    let query = this.supabase
      .from('document_templates')
      .select('*')
      .eq('business_id', businessId)
      .eq('is_active', true);

    if (templateType) {
      query = query.eq('template_type', templateType);
    }

    query = query.order('template_name');

    const { data, error } = await query;
    if (error) throw error;

    return data || [];
  }

  async generateDocument(
    businessId: string,
    templateId: string,
    data: any,
    options?: {
      documentName?: string;
      sourceType?: string;
      sourceId?: string;
    }
  ): Promise<Document> {
    const user = await this.supabase.auth.getUser();

    // Get template
    const template = await this.getTemplate(templateId);
    if (!template) throw new Error('Template not found');

    // Generate document name if not provided
    const documentName = options?.documentName || 
      `${template.template_type}_${Date.now()}.pdf`;

    // Create document record
    const { data: document, error: docError } = await this.supabase
      .from('documents')
      .insert({
        business_id: businessId,
        template_id: templateId,
        document_name: documentName,
        document_type: template.template_type,
        file_path: '', // Will be updated after generation
        file_size: 0, // Will be updated after generation
        source_type: options?.sourceType,
        source_id: options?.sourceId,
        generation_data: data,
        generation_status: 'processing',
        generated_by: user.data.user?.id,
      })
      .select()
      .single();

    if (docError) throw docError;

    try {
      // Generate PDF (this would typically call an Edge Function)
      const pdfResult = await this.generatePDF(template, data);
      
      // Upload to storage
      const filePath = await this.uploadFile(
        businessId,
        documentName,
        pdfResult.buffer,
        'application/pdf'
      );

      // Update document record
      const { data: updatedDoc, error: updateError } = await this.supabase
        .from('documents')
        .update({
          file_path: filePath,
          file_size: pdfResult.buffer.byteLength,
          generation_status: 'completed',
          updated_at: new Date().toISOString(),
        })
        .eq('id', document.id)
        .select()
        .single();

      if (updateError) throw updateError;
      return updatedDoc;

    } catch (error) {
      // Update document with error status
      await this.supabase
        .from('documents')
        .update({
          generation_status: 'failed',
          error_message: error.message,
        })
        .eq('id', document.id);

      throw error;
    }
  }

  async getDocuments(
    businessId: string,
    filters?: {
      document_type?: string;
      source_type?: string;
      source_id?: string;
      date_from?: string;
      date_to?: string;
    },
    pagination?: { page: number; limit: number }
  ): Promise<{ documents: Document[]; total: number }> {
    let query = this.supabase
      .from('documents')
      .select('*', { count: 'exact' })
      .eq('business_id', businessId);

    // Apply filters
    if (filters?.document_type) {
      query = query.eq('document_type', filters.document_type);
    }
    if (filters?.source_type) {
      query = query.eq('source_type', filters.source_type);
    }
    if (filters?.source_id) {
      query = query.eq('source_id', filters.source_id);
    }
    if (filters?.date_from) {
      query = query.gte('created_at', filters.date_from);
    }
    if (filters?.date_to) {
      query = query.lte('created_at', filters.date_to);
    }

    // Apply pagination
    if (pagination) {
      const offset = (pagination.page - 1) * pagination.limit;
      query = query.range(offset, offset + pagination.limit - 1);
    }

    query = query.order('created_at', { ascending: false });

    const { data, error, count } = await query;
    if (error) throw error;

    return {
      documents: data || [],
      total: count || 0,
    };
  }

  async downloadDocument(documentId: string): Promise<Blob> {
    // Get document details
    const { data: document, error } = await this.supabase
      .from('documents')
      .select('file_path, business_id')
      .eq('id', documentId)
      .single();

    if (error || !document) throw new Error('Document not found');

    // Download file from storage
    const { data: fileData, error: downloadError } = await this.supabase.storage
      .from('documents')
      .download(document.file_path);

    if (downloadError) throw downloadError;

    // Update download count
    await this.supabase
      .from('documents')
      .update({
        download_count: this.supabase.sql`download_count + 1`,
        last_downloaded_at: new Date().toISOString(),
      })
      .eq('id', documentId);

    return fileData;
  }

  async shareDocument(
    documentId: string,
    shareData: {
      email?: string;
      user_id?: string;
      permissions?: {
        can_view?: boolean;
        can_download?: boolean;
        can_comment?: boolean;
      };
      expires_at?: string;
    }
  ): Promise<void> {
    const user = await this.supabase.auth.getUser();

    const { error } = await this.supabase
      .from('document_shares')
      .insert({
        document_id: documentId,
        shared_with_email: shareData.email,
        shared_with_user_id: shareData.user_id,
        can_view: shareData.permissions?.can_view ?? true,
        can_download: shareData.permissions?.can_download ?? true,
        can_comment: shareData.permissions?.can_comment ?? false,
        expires_at: shareData.expires_at,
        shared_by: user.data.user?.id,
      });

    if (error) throw error;

    // Send notification email if email is provided
    if (shareData.email) {
      await this.sendShareNotification(documentId, shareData.email);
    }
  }

  async uploadFile(
    businessId: string,
    fileName: string,
    fileBuffer: ArrayBuffer,
    mimeType: string,
    category?: string
  ): Promise<string> {
    const user = await this.supabase.auth.getUser();
    const fileExt = fileName.split('.').pop();
    const uniqueFileName = `${businessId}/${Date.now()}_${Math.random().toString(36).substring(7)}.${fileExt}`;

    // Upload to Supabase Storage
    const { error: uploadError } = await this.supabase.storage
      .from('documents')
      .upload(uniqueFileName, fileBuffer, {
        contentType: mimeType,
      });

    if (uploadError) throw uploadError;

    // Create file storage record
    await this.supabase
      .from('file_storage')
      .insert({
        business_id: businessId,
        file_name: uniqueFileName,
        original_name: fileName,
        file_path: uniqueFileName,
        file_size: fileBuffer.byteLength,
        mime_type: mimeType,
        category: category || 'document',
        uploaded_by: user.data.user?.id,
      });

    return uniqueFileName;
  }

  async deleteDocument(documentId: string): Promise<void> {
    // Get document details
    const { data: document } = await this.supabase
      .from('documents')
      .select('file_path')
      .eq('id', documentId)
      .single();

    if (document?.file_path) {
      // Delete from storage
      await this.supabase.storage
        .from('documents')
        .remove([document.file_path]);
    }

    // Delete document record
    const { error } = await this.supabase
      .from('documents')
      .delete()
      .eq('id', documentId);

    if (error) throw error;
  }

  async getTemplate(templateId: string): Promise<DocumentTemplate | null> {
    const { data, error } = await this.supabase
      .from('document_templates')
      .select('*')
      .eq('id', templateId)
      .single();

    if (error) return null;
    return data;
  }

  private async generatePDF(template: DocumentTemplate, data: any): Promise<{ buffer: ArrayBuffer }> {
    // This would typically call a Supabase Edge Function or external service
    // for PDF generation using libraries like Puppeteer, jsPDF, or PDFKit
    const response = await fetch('/api/documents/generate-pdf', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        template,
        data,
      }),
    });

    if (!response.ok) throw new Error('PDF generation failed');
    
    const buffer = await response.arrayBuffer();
    return { buffer };
  }

  private async sendShareNotification(documentId: string, email: string): Promise<void> {
    // Send email notification about document share
    await fetch('/api/documents/send-share-notification', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        documentId,
        email,
      }),
    });
  }
}
```

## Security Measures
- **Access Control**: Role-based permissions for document access
- **Secure Storage**: Encrypted file storage with access tokens
- **Document Sharing**: Controlled sharing with expiration and permissions
- **File Validation**: Comprehensive file type and size validation
- **Audit Trail**: Complete tracking of document operations
- **Public Access**: Secure public sharing with access tokens

## Integration Points
- **Invoice Management**: Automatic PDF generation for invoices
- **Business Management**: Business-scoped document templates
- **User Management**: User permissions and document access
- **Email Service**: Document sharing notifications
- **File Storage**: Supabase Storage integration

## Error Handling & Validation
- **Template Validation**: Comprehensive template structure validation
- **File Validation**: File type, size, and content validation
- **Generation Error Handling**: Robust PDF generation error handling
- **Storage Error Handling**: File upload and storage error management
- **Access Validation**: Document access permission validation

## Testing Strategy
- **Unit Tests**: Document service methods and template validation
- **Integration Tests**: PDF generation and file storage
- **E2E Tests**: Complete document workflows
- **Performance Tests**: Large document generation and storage
- **Security Tests**: Access control and sharing permissions

## Implementation Tasks
1. **Database Schema Setup**
   - Create document-related tables and indexes
   - Implement RLS policies for secure access
   - Set up file storage metadata tracking

2. **Document Service Development**
   - Build DocumentService with all operations
   - Implement PDF generation system
   - Create file upload and storage management

3. **Template System**
   - Build template editor interface
   - Implement template validation and versioning
   - Create template variable system

4. **PDF Generation**
   - Set up PDF generation service (Edge Function)
   - Implement template rendering engine
   - Create PDF optimization and compression

5. **File Management**
   - Build file upload and management interface
   - Implement file organization and categorization
   - Create bulk file operations

6. **Document Sharing**
   - Build document sharing system
   - Implement access control and permissions
   - Create sharing notifications and tracking

## Dependencies
- **Business Management**: Business context for document templates
- **User Management**: User permissions and access control
- **File Storage**: Supabase Storage for file management
- **Email Service**: Document sharing notifications
- **PDF Generation**: External PDF generation service

## Success Criteria
- ✅ Document templates can be created and customized
- ✅ PDF generation works reliably for all document types
- ✅ File upload and storage works seamlessly
- ✅ Document sharing and access control works properly
- ✅ Template editor is intuitive and functional
- ✅ Document versioning and history is maintained
- ✅ Performance is acceptable for large documents
- ✅ All document operations are properly secured
