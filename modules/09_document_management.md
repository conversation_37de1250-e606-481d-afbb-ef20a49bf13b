# Document Management & Templates

## Overview
This module handles all document-related functionality including PDF generation, template management, file storage, and document workflows. It provides comprehensive document management capabilities with support for custom templates, automated document generation, and secure file handling.

## Core Functionalities
- PDF generation for invoices, quotes, and reports
- Customizable document templates with variables
- File upload and storage management
- Document versioning and history
- Template editor with drag-and-drop interface
- Automated document generation workflows
- Document sharing and access control
- Digital signatures and document approval
- Bulk document operations
- Document analytics and tracking

## Technical Specifications

### Database Schema
```sql
-- Document templates
CREATE TABLE app.document_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    template_name TEXT NOT NULL,
    template_type TEXT NOT NULL, -- 'invoice', 'quote', 'receipt', 'report'
    description TEXT,
    
    -- Template content
    template_data JSONB NOT NULL, -- Template structure and styling
    variables JSONB, -- Available variables for this template
    
    -- Layout settings
    page_size TEXT DEFAULT 'A4',
    orientation TEXT DEFAULT 'portrait',
    margins JSONB DEFAULT '{"top": 20, "right": 20, "bottom": 20, "left": 20}',
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE,
    version INTEGER DEFAULT 1,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    updated_by UUID REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, template_name, template_type)
);

-- Generated documents
CREATE TABLE app.documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    template_id UUID REFERENCES app.document_templates(id),
    
    -- Document details
    document_name TEXT NOT NULL,
    document_type TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type TEXT NOT NULL DEFAULT 'application/pdf',
    
    -- Source reference
    source_type TEXT, -- 'invoice', 'quote', 'manual'
    source_id UUID,
    
    -- Generation details
    generation_data JSONB, -- Data used to generate the document
    generation_status TEXT DEFAULT 'completed' CHECK (generation_status IN ('pending', 'processing', 'completed', 'failed')),
    error_message TEXT,
    
    -- Access control
    is_public BOOLEAN DEFAULT FALSE,
    access_token TEXT, -- For public sharing
    expires_at TIMESTAMPTZ,
    
    -- Tracking
    download_count INTEGER DEFAULT 0,
    last_downloaded_at TIMESTAMPTZ,
    
    generated_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Document shares for controlled access
CREATE TABLE app.document_shares (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    document_id UUID NOT NULL REFERENCES app.documents(id) ON DELETE CASCADE,
    
    shared_with_email TEXT,
    shared_with_user_id UUID REFERENCES app.users(id),
    
    -- Permissions
    can_view BOOLEAN DEFAULT TRUE,
    can_download BOOLEAN DEFAULT TRUE,
    can_comment BOOLEAN DEFAULT FALSE,
    
    -- Access tracking
    access_count INTEGER DEFAULT 0,
    last_accessed_at TIMESTAMPTZ,
    
    -- Expiry
    expires_at TIMESTAMPTZ,
    
    shared_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- File storage metadata
CREATE TABLE app.file_storage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    file_name TEXT NOT NULL,
    original_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type TEXT NOT NULL,
    
    -- File categorization
    category TEXT, -- 'document', 'image', 'attachment'
    tags TEXT[],
    
    -- Storage details
    storage_provider TEXT DEFAULT 'supabase',
    storage_bucket TEXT,
    
    -- Access control
    is_public BOOLEAN DEFAULT FALSE,
    
    uploaded_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Document approval workflows
CREATE TABLE app.document_approvals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    document_id UUID NOT NULL REFERENCES app.documents(id) ON DELETE CASCADE,
    
    approver_id UUID NOT NULL REFERENCES app.users(id),
    approval_status TEXT NOT NULL DEFAULT 'pending' CHECK (approval_status IN ('pending', 'approved', 'rejected')),
    
    comments TEXT,
    approved_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_document_templates_business_type ON app.document_templates(business_id, template_type);
CREATE INDEX idx_documents_business_id ON app.documents(business_id);
CREATE INDEX idx_documents_source ON app.documents(source_type, source_id);
CREATE INDEX idx_documents_created_at ON app.documents(created_at);
CREATE INDEX idx_document_shares_document_id ON app.document_shares(document_id);
CREATE INDEX idx_file_storage_business_id ON app.file_storage(business_id);
CREATE INDEX idx_file_storage_category ON app.file_storage(category);
CREATE INDEX idx_document_approvals_document_id ON app.document_approvals(document_id);

-- Enable RLS
ALTER TABLE app.document_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.document_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.file_storage ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.document_approvals ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Business members can manage templates"
ON app.document_templates FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);

CREATE POLICY "Business members can view documents"
ON app.documents FOR SELECT
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
    OR is_public = TRUE
);

CREATE POLICY "Business members can manage documents"
ON app.documents FOR INSERT, UPDATE, DELETE
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);
```

### Document Service Architecture

**Core Service: DocumentService**
- **Primary Responsibilities**: Document template management, PDF generation, file storage, document sharing
- **Key Methods**:
  - `createTemplate(businessId, templateData)` - Custom document template creation
  - `updateTemplate(templateId, updates)` - Template modification and versioning
  - `generateDocument(businessId, templateId, data, options)` - PDF generation from templates
  - `getDocuments(businessId, filters, pagination)` - Document listing and filtering
  - `downloadDocument(documentId)` - Secure document download with tracking
  - `shareDocument(documentId, shareData)` - Document sharing with permissions
  - `uploadFile(businessId, fileName, fileBuffer, mimeType)` - File upload and storage
  - `deleteDocument(documentId)` - Document deletion with storage cleanup
  - `getTemplate(templateId)` - Template retrieval for generation

**Implementation Approach**:
- Build flexible template system supporting multiple document types (invoices, quotes, reports)
- Implement PDF generation service using Edge Functions with Puppeteer or similar
- Create secure file storage with access control and sharing permissions
- Establish document versioning and approval workflows
- Build template editor with drag-and-drop interface and variable injection

### Document Management Strategy

**Template System**:
- **Visual Editor**: Drag-and-drop template builder with live preview
- **Variable Injection**: Dynamic content insertion from business data
- **Multi-Format Support**: PDF, HTML, and print-ready document generation
- **Version Control**: Template versioning with rollback capabilities
- **Brand Consistency**: Business branding integration and style management

**PDF Generation Pipeline**:
- **Template Processing**: Variable substitution and content rendering
- **Layout Engine**: Professional document layout with responsive design
- **Asset Management**: Logo, signature, and image embedding
- **Quality Control**: PDF optimization and compression
- **Batch Processing**: Bulk document generation for mass operations

**File Storage & Security**:
- **Secure Storage**: Encrypted file storage with access tokens
- **Permission System**: Granular sharing permissions and expiration dates
- **Access Tracking**: Download tracking and audit trails
- **Storage Optimization**: File compression and CDN integration
- **Backup Strategy**: Automated backup and disaster recovery

## Security Measures
- **Access Control**: Role-based permissions for document access
- **Secure Storage**: Encrypted file storage with access tokens
- **Document Sharing**: Controlled sharing with expiration and permissions
- **File Validation**: Comprehensive file type and size validation
- **Audit Trail**: Complete tracking of document operations
- **Public Access**: Secure public sharing with access tokens

## Integration Points
- **Invoice Management**: Automatic PDF generation for invoices
- **Business Management**: Business-scoped document templates
- **User Management**: User permissions and document access
- **Email Service**: Document sharing notifications
- **File Storage**: Supabase Storage integration

## Error Handling & Validation
- **Template Validation**: Comprehensive template structure validation
- **File Validation**: File type, size, and content validation
- **Generation Error Handling**: Robust PDF generation error handling
- **Storage Error Handling**: File upload and storage error management
- **Access Validation**: Document access permission validation

## Testing Strategy
- **Unit Tests**: Document service methods and template validation
- **Integration Tests**: PDF generation and file storage
- **E2E Tests**: Complete document workflows
- **Performance Tests**: Large document generation and storage
- **Security Tests**: Access control and sharing permissions

## Implementation Tasks
1. **Database Schema Setup**
   - Create document-related tables and indexes
   - Implement RLS policies for secure access
   - Set up file storage metadata tracking

2. **Document Service Development**
   - Build DocumentService with all operations
   - Implement PDF generation system
   - Create file upload and storage management

3. **Template System**
   - Build template editor interface
   - Implement template validation and versioning
   - Create template variable system

4. **PDF Generation**
   - Set up PDF generation service (Edge Function)
   - Implement template rendering engine
   - Create PDF optimization and compression

5. **File Management**
   - Build file upload and management interface
   - Implement file organization and categorization
   - Create bulk file operations

6. **Document Sharing**
   - Build document sharing system
   - Implement access control and permissions
   - Create sharing notifications and tracking

## Dependencies
- **Business Management**: Business context for document templates
- **User Management**: User permissions and access control
- **File Storage**: Supabase Storage for file management
- **Email Service**: Document sharing notifications
- **PDF Generation**: External PDF generation service

## Success Criteria
- ✅ Document templates can be created and customized
- ✅ PDF generation works reliably for all document types
- ✅ File upload and storage works seamlessly
- ✅ Document sharing and access control works properly
- ✅ Template editor is intuitive and functional
- ✅ Document versioning and history is maintained
- ✅ Performance is acceptable for large documents
- ✅ All document operations are properly secured
