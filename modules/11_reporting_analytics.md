# Reporting & Analytics Dashboard

## Overview
This module provides comprehensive business intelligence and analytics capabilities, offering real-time insights into business performance, financial metrics, customer behavior, and operational efficiency. It includes interactive dashboards, customizable reports, and automated insights to help businesses make data-driven decisions.

## Core Functionalities
- Real-time business performance dashboards
- Financial reporting and analysis
- Customer analytics and insights
- Invoice and payment analytics
- Revenue forecasting and trends
- Customizable report builder
- Automated report scheduling and delivery
- Data export capabilities (PDF, Excel, CSV)
- Key Performance Indicators (KPI) tracking
- Comparative analysis and benchmarking

## Technical Specifications

### Database Schema
```sql
-- Report definitions table
CREATE TABLE app.reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    report_type TEXT NOT NULL,
    configuration JSONB NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    is_scheduled BOOLEAN DEFAULT FALSE,
    schedule_config JSONB,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, name)
);

-- Report executions for tracking and caching
CREATE TABLE app.report_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    report_id UUID NOT NULL REFERENCES app.reports(id) ON DELETE CASCADE,
    executed_by UUID REFERENCES app.users(id),
    execution_time INTERVAL,
    result_data JSONB,
    parameters JSONB,
    status TEXT NOT NULL DEFAULT 'completed',
    error_message TEXT,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Dashboard configurations
CREATE TABLE app.dashboards (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    user_id UUID REFERENCES app.users(id), -- NULL for business-wide dashboards
    name TEXT NOT NULL,
    description TEXT,
    layout_config JSONB NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- KPI definitions and tracking
CREATE TABLE app.kpis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    calculation_method TEXT NOT NULL,
    target_value NUMERIC(15, 2),
    warning_threshold NUMERIC(15, 2),
    critical_threshold NUMERIC(15, 2),
    unit TEXT,
    category TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, name)
);

-- KPI values over time
CREATE TABLE app.kpi_values (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    kpi_id UUID NOT NULL REFERENCES app.kpis(id) ON DELETE CASCADE,
    period_start TIMESTAMPTZ NOT NULL,
    period_end TIMESTAMPTZ NOT NULL,
    value NUMERIC(15, 2) NOT NULL,
    calculated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Analytics events for tracking user behavior
CREATE TABLE app.analytics_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    user_id UUID REFERENCES app.users(id),
    event_type TEXT NOT NULL,
    event_data JSONB,
    session_id TEXT,
    ip_address INET,
    user_agent TEXT,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_reports_business_id ON app.reports(business_id);
CREATE INDEX idx_report_executions_report_id ON app.report_executions(report_id);
CREATE INDEX idx_report_executions_created_at ON app.report_executions(created_at);
CREATE INDEX idx_dashboards_business_user ON app.dashboards(business_id, user_id);
CREATE INDEX idx_kpis_business_id ON app.kpis(business_id);
CREATE INDEX idx_kpi_values_kpi_period ON app.kpi_values(kpi_id, period_start, period_end);
CREATE INDEX idx_analytics_events_business_type ON app.analytics_events(business_id, event_type);
CREATE INDEX idx_analytics_events_created_at ON app.analytics_events(created_at);

-- Enable RLS
ALTER TABLE app.reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.report_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.dashboards ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.kpis ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.kpi_values ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.analytics_events ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Business members can view reports"
ON app.reports FOR SELECT
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);

CREATE POLICY "Business admins can manage reports"
ON app.reports FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() 
        AND role IN ('owner', 'admin', 'manager')
        AND is_active = TRUE
    )
);
```

### Analytics Service
```typescript
// lib/analytics.ts
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

export interface DashboardMetrics {
  totalRevenue: number;
  monthlyRevenue: number;
  totalInvoices: number;
  paidInvoices: number;
  overdueInvoices: number;
  totalCustomers: number;
  activeCustomers: number;
  averageInvoiceValue: number;
  paymentRate: number;
  revenueGrowth: number;
}

export interface RevenueData {
  period: string;
  revenue: number;
  invoices: number;
  customers: number;
}

export interface CustomerAnalytics {
  customerId: string;
  customerName: string;
  totalRevenue: number;
  invoiceCount: number;
  averageInvoiceValue: number;
  lastInvoiceDate: string;
  paymentRate: number;
}

export interface Report {
  id: string;
  name: string;
  description?: string;
  report_type: string;
  configuration: any;
  is_public: boolean;
  is_scheduled: boolean;
  schedule_config?: any;
  created_at: string;
}

export class AnalyticsService {
  private supabase = createClientComponentClient();

  async getDashboardMetrics(businessId: string, dateRange?: {
    start: string;
    end: string;
  }): Promise<DashboardMetrics> {
    const { start, end } = dateRange || this.getDefaultDateRange();

    // Get revenue metrics
    const { data: revenueData } = await this.supabase
      .from('invoices')
      .select('total_amount, paid_amount, status, invoice_date')
      .eq('business_id', businessId)
      .gte('invoice_date', start)
      .lte('invoice_date', end);

    // Get customer metrics
    const { data: customerData } = await this.supabase
      .from('customers')
      .select('id, created_at')
      .eq('business_id', businessId);

    // Calculate metrics
    const totalRevenue = revenueData?.reduce((sum, inv) => sum + inv.paid_amount, 0) || 0;
    const totalInvoices = revenueData?.length || 0;
    const paidInvoices = revenueData?.filter(inv => inv.status === 'paid').length || 0;
    const overdueInvoices = revenueData?.filter(inv => 
      inv.status === 'overdue' || 
      (inv.status === 'sent' && new Date(inv.due_date) < new Date())
    ).length || 0;

    const totalCustomers = customerData?.length || 0;
    const activeCustomers = await this.getActiveCustomersCount(businessId, start, end);
    const averageInvoiceValue = totalInvoices > 0 ? totalRevenue / totalInvoices : 0;
    const paymentRate = totalInvoices > 0 ? (paidInvoices / totalInvoices) * 100 : 0;

    // Calculate growth (compare with previous period)
    const previousPeriodRevenue = await this.getPreviousPeriodRevenue(businessId, start, end);
    const revenueGrowth = previousPeriodRevenue > 0 
      ? ((totalRevenue - previousPeriodRevenue) / previousPeriodRevenue) * 100 
      : 0;

    // Get monthly revenue for current period
    const monthlyRevenue = await this.getMonthlyRevenue(businessId);

    return {
      totalRevenue,
      monthlyRevenue,
      totalInvoices,
      paidInvoices,
      overdueInvoices,
      totalCustomers,
      activeCustomers,
      averageInvoiceValue,
      paymentRate,
      revenueGrowth,
    };
  }

  async getRevenueChart(businessId: string, period: 'daily' | 'weekly' | 'monthly' = 'monthly', months = 12): Promise<RevenueData[]> {
    const { data, error } = await this.supabase.rpc('get_revenue_chart', {
      p_business_id: businessId,
      p_period: period,
      p_months: months,
    });

    if (error) throw error;
    return data || [];
  }

  async getTopCustomers(businessId: string, limit = 10): Promise<CustomerAnalytics[]> {
    const { data, error } = await this.supabase.rpc('get_top_customers', {
      p_business_id: businessId,
      p_limit: limit,
    });

    if (error) throw error;
    return data || [];
  }

  async getInvoiceStatusDistribution(businessId: string): Promise<{ status: string; count: number; percentage: number }[]> {
    const { data, error } = await this.supabase
      .from('invoices')
      .select('status')
      .eq('business_id', businessId);

    if (error) throw error;

    const statusCounts = data.reduce((acc, invoice) => {
      acc[invoice.status] = (acc[invoice.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const total = data.length;
    return Object.entries(statusCounts).map(([status, count]) => ({
      status,
      count,
      percentage: total > 0 ? (count / total) * 100 : 0,
    }));
  }

  async getPaymentTrends(businessId: string, months = 6): Promise<{
    period: string;
    onTime: number;
    late: number;
    overdue: number;
  }[]> {
    const { data, error } = await this.supabase.rpc('get_payment_trends', {
      p_business_id: businessId,
      p_months: months,
    });

    if (error) throw error;
    return data || [];
  }

  async createReport(businessId: string, reportData: {
    name: string;
    description?: string;
    report_type: string;
    configuration: any;
    is_scheduled?: boolean;
    schedule_config?: any;
  }): Promise<Report> {
    const { data, error } = await this.supabase
      .from('reports')
      .insert({
        business_id: businessId,
        ...reportData,
        created_by: (await this.supabase.auth.getUser()).data.user?.id,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async executeReport(reportId: string, parameters?: any): Promise<any> {
    const startTime = Date.now();

    try {
      // Get report configuration
      const { data: report, error: reportError } = await this.supabase
        .from('reports')
        .select('*')
        .eq('id', reportId)
        .single();

      if (reportError) throw reportError;

      // Execute report based on type
      let resultData;
      switch (report.report_type) {
        case 'revenue_summary':
          resultData = await this.executeRevenueSummaryReport(report.business_id, parameters);
          break;
        case 'customer_analysis':
          resultData = await this.executeCustomerAnalysisReport(report.business_id, parameters);
          break;
        case 'invoice_aging':
          resultData = await this.executeInvoiceAgingReport(report.business_id, parameters);
          break;
        default:
          throw new Error(`Unknown report type: ${report.report_type}`);
      }

      const executionTime = Date.now() - startTime;

      // Save execution record
      await this.supabase
        .from('report_executions')
        .insert({
          report_id: reportId,
          executed_by: (await this.supabase.auth.getUser()).data.user?.id,
          execution_time: `${executionTime}ms`,
          result_data: resultData,
          parameters,
          status: 'completed',
        });

      return resultData;
    } catch (error) {
      const executionTime = Date.now() - startTime;

      // Save error record
      await this.supabase
        .from('report_executions')
        .insert({
          report_id: reportId,
          executed_by: (await this.supabase.auth.getUser()).data.user?.id,
          execution_time: `${executionTime}ms`,
          parameters,
          status: 'error',
          error_message: error.message,
        });

      throw error;
    }
  }

  async trackEvent(businessId: string, eventType: string, eventData?: any): Promise<void> {
    const user = await this.supabase.auth.getUser();
    
    await this.supabase
      .from('analytics_events')
      .insert({
        business_id: businessId,
        user_id: user.data.user?.id,
        event_type: eventType,
        event_data: eventData,
        session_id: this.getSessionId(),
        ip_address: await this.getClientIP(),
        user_agent: navigator.userAgent,
      });
  }

  async exportReport(reportId: string, format: 'pdf' | 'excel' | 'csv', parameters?: any): Promise<Blob> {
    const reportData = await this.executeReport(reportId, parameters);
    
    // This would typically call an Edge Function for export generation
    const response = await fetch('/api/reports/export', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        reportId,
        format,
        data: reportData,
        parameters,
      }),
    });

    if (!response.ok) throw new Error('Export failed');
    return response.blob();
  }

  private async getActiveCustomersCount(businessId: string, start: string, end: string): Promise<number> {
    const { count } = await this.supabase
      .from('invoices')
      .select('customer_id', { count: 'exact', head: true })
      .eq('business_id', businessId)
      .gte('invoice_date', start)
      .lte('invoice_date', end);

    return count || 0;
  }

  private async getPreviousPeriodRevenue(businessId: string, start: string, end: string): Promise<number> {
    const startDate = new Date(start);
    const endDate = new Date(end);
    const periodLength = endDate.getTime() - startDate.getTime();
    
    const prevStart = new Date(startDate.getTime() - periodLength).toISOString().split('T')[0];
    const prevEnd = new Date(endDate.getTime() - periodLength).toISOString().split('T')[0];

    const { data } = await this.supabase
      .from('invoices')
      .select('paid_amount')
      .eq('business_id', businessId)
      .gte('invoice_date', prevStart)
      .lte('invoice_date', prevEnd);

    return data?.reduce((sum, inv) => sum + inv.paid_amount, 0) || 0;
  }

  private async getMonthlyRevenue(businessId: string): Promise<number> {
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const { data } = await this.supabase
      .from('invoices')
      .select('paid_amount')
      .eq('business_id', businessId)
      .gte('invoice_date', startOfMonth.toISOString().split('T')[0]);

    return data?.reduce((sum, inv) => sum + inv.paid_amount, 0) || 0;
  }

  private getDefaultDateRange() {
    const end = new Date();
    const start = new Date();
    start.setMonth(start.getMonth() - 12); // Last 12 months

    return {
      start: start.toISOString().split('T')[0],
      end: end.toISOString().split('T')[0],
    };
  }

  private async executeRevenueSummaryReport(businessId: string, parameters: any): Promise<any> {
    // Implementation for revenue summary report
    return this.getDashboardMetrics(businessId, parameters?.dateRange);
  }

  private async executeCustomerAnalysisReport(businessId: string, parameters: any): Promise<any> {
    // Implementation for customer analysis report
    return this.getTopCustomers(businessId, parameters?.limit || 50);
  }

  private async executeInvoiceAgingReport(businessId: string, parameters: any): Promise<any> {
    // Implementation for invoice aging report
    const { data, error } = await this.supabase.rpc('get_invoice_aging', {
      p_business_id: businessId,
    });

    if (error) throw error;
    return data;
  }

  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('analytics_session_id');
    if (!sessionId) {
      sessionId = crypto.randomUUID();
      sessionStorage.setItem('analytics_session_id', sessionId);
    }
    return sessionId;
  }

  private async getClientIP(): Promise<string> {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch {
      return 'unknown';
    }
  }
}
```

## Security Measures
- **Data Access Control**: Business-scoped analytics data
- **Report Permissions**: Role-based report access and creation
- **Data Privacy**: Anonymized analytics where appropriate
- **Execution Logging**: Complete audit trail of report executions
- **Parameter Validation**: Secure parameter handling for reports
- **Export Security**: Controlled data export with audit trails

## Integration Points
- **All Business Modules**: Data source for analytics
- **User Management**: User behavior and activity tracking
- **Invoice Management**: Financial metrics and trends
- **Customer Management**: Customer analytics and insights
- **Payment Processing**: Payment behavior analysis

## Error Handling & Validation
- **Report Validation**: Configuration and parameter validation
- **Data Quality**: Handling of missing or invalid data
- **Performance Monitoring**: Query timeout and optimization
- **Export Error Handling**: Graceful failure handling for exports
- **Calculation Accuracy**: Validation of financial calculations

## Testing Strategy
- **Unit Tests**: Analytics calculations and report logic
- **Integration Tests**: Database queries and data accuracy
- **Performance Tests**: Large dataset handling and query optimization
- **E2E Tests**: Complete reporting workflows
- **Data Tests**: Accuracy of metrics and calculations

## Implementation Tasks
1. **Database Schema Setup**
   - Create analytics and reporting tables
   - Implement RLS policies for data access
   - Set up indexes for query performance

2. **Analytics Service Development**
   - Build AnalyticsService with all metrics
   - Implement report execution engine
   - Create data aggregation functions

3. **Dashboard Frontend**
   - Build interactive dashboard components
   - Create chart and visualization components
   - Implement real-time data updates

4. **Report Builder**
   - Create report configuration interface
   - Build report execution and viewing
   - Implement report scheduling system

5. **Export Functionality**
   - Build PDF/Excel/CSV export services
   - Create export templates and formatting
   - Implement bulk export capabilities

6. **Advanced Analytics**
   - Implement predictive analytics
   - Build trend analysis and forecasting
   - Create automated insights and alerts

## Dependencies
- **All Business Modules**: Source data for analytics
- **Document Management**: Report export and storage
- **Email Service**: Scheduled report delivery
- **Background Jobs**: Report scheduling and execution

## Success Criteria
- ✅ Real-time dashboard displays accurate metrics
- ✅ Reports execute within acceptable time limits
- ✅ Export functionality works for all formats
- ✅ Scheduled reports are delivered automatically
- ✅ Analytics provide actionable business insights
- ✅ Performance is acceptable for large datasets
- ✅ All calculations are accurate and auditable
