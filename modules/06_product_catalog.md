# Product & Service Catalog

## Overview
This module manages the complete product and service catalog for businesses, including inventory tracking, pricing management, categories, and product variants. It provides a flexible foundation for invoice line items and supports both physical products and services with comprehensive metadata and analytics.

## Core Functionalities
- Product and service management with detailed information
- Category and subcategory organization
- Multiple pricing tiers and currency support
- Product variants and options (size, color, etc.)
- Inventory tracking and stock management
- Product images and document attachments
- Bulk import/export capabilities
- Product analytics and performance tracking
- Tax configuration per product
- Supplier and vendor management

## Technical Specifications

### Database Schema
```sql
-- Product categories for organization
CREATE TABLE app.product_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES app.product_categories(id),
    
    name TEXT NOT NULL,
    description TEXT,
    slug TEXT NOT NULL,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, slug)
);

-- Main products table
CREATE TABLE app.products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    category_id UUID REFERENCES app.product_categories(id),
    
    -- Basic information
    name TEXT NOT NULL,
    description TEXT,
    sku TEXT,
    barcode TEXT,
    product_type TEXT NOT NULL DEFAULT 'product' CHECK (product_type IN ('product', 'service', 'digital')),
    
    -- Pricing
    price NUMERIC(12, 2) NOT NULL CHECK (price >= 0),
    cost NUMERIC(12, 2) CHECK (cost >= 0),
    currency TEXT NOT NULL DEFAULT 'USD',
    
    -- Tax information
    tax_rate NUMERIC(5, 4) DEFAULT 0,
    tax_inclusive BOOLEAN DEFAULT FALSE,
    tax_category TEXT,
    
    -- Inventory (for physical products)
    track_inventory BOOLEAN DEFAULT FALSE,
    stock_quantity INTEGER DEFAULT 0,
    low_stock_threshold INTEGER DEFAULT 0,
    allow_backorder BOOLEAN DEFAULT FALSE,
    
    -- Physical attributes
    weight NUMERIC(8, 3),
    weight_unit TEXT DEFAULT 'kg',
    dimensions JSONB, -- {length, width, height, unit}
    
    -- Status and visibility
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'archived')),
    is_featured BOOLEAN DEFAULT FALSE,
    
    -- Metadata
    tags TEXT[],
    notes TEXT,
    
    -- Tracking
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, sku) WHERE sku IS NOT NULL
);

-- Product variants (for products with options like size, color)
CREATE TABLE app.product_variants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES app.products(id) ON DELETE CASCADE,
    
    name TEXT NOT NULL,
    sku TEXT,
    barcode TEXT,
    
    -- Pricing (can override parent product)
    price NUMERIC(12, 2),
    cost NUMERIC(12, 2),
    
    -- Inventory
    stock_quantity INTEGER DEFAULT 0,
    
    -- Variant attributes
    attributes JSONB NOT NULL, -- {color: "red", size: "large"}
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(product_id, sku) WHERE sku IS NOT NULL
);

-- Product pricing tiers for different customer types
CREATE TABLE app.product_pricing (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES app.products(id) ON DELETE CASCADE,
    variant_id UUID REFERENCES app.product_variants(id),
    
    tier_name TEXT NOT NULL,
    min_quantity INTEGER DEFAULT 1,
    price NUMERIC(12, 2) NOT NULL,
    currency TEXT NOT NULL DEFAULT 'USD',
    
    valid_from TIMESTAMPTZ,
    valid_until TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(product_id, variant_id, tier_name, min_quantity)
);

-- Product images and media
CREATE TABLE app.product_media (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES app.products(id) ON DELETE CASCADE,
    variant_id UUID REFERENCES app.product_variants(id),
    
    media_type TEXT NOT NULL CHECK (media_type IN ('image', 'video', 'document')),
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type TEXT NOT NULL,
    
    alt_text TEXT,
    sort_order INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT FALSE,
    
    uploaded_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Inventory movements for tracking stock changes
CREATE TABLE app.inventory_movements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES app.products(id) ON DELETE CASCADE,
    variant_id UUID REFERENCES app.product_variants(id),
    
    movement_type TEXT NOT NULL CHECK (movement_type IN ('in', 'out', 'adjustment', 'transfer')),
    quantity INTEGER NOT NULL,
    reference_type TEXT, -- 'invoice', 'purchase', 'adjustment', etc.
    reference_id UUID,
    
    notes TEXT,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Suppliers/vendors for products
CREATE TABLE app.suppliers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    name TEXT NOT NULL,
    contact_person TEXT,
    email TEXT,
    phone TEXT,
    website TEXT,
    
    address JSONB,
    
    payment_terms INTEGER DEFAULT 30,
    currency TEXT DEFAULT 'USD',
    
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    notes TEXT,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Product-supplier relationships
CREATE TABLE app.product_suppliers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES app.products(id) ON DELETE CASCADE,
    supplier_id UUID NOT NULL REFERENCES app.suppliers(id) ON DELETE CASCADE,
    
    supplier_sku TEXT,
    supplier_price NUMERIC(12, 2),
    lead_time_days INTEGER,
    minimum_order_quantity INTEGER DEFAULT 1,
    
    is_primary BOOLEAN DEFAULT FALSE,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(product_id, supplier_id)
);

-- Indexes for performance
CREATE INDEX idx_product_categories_business_id ON app.product_categories(business_id);
CREATE INDEX idx_product_categories_parent_id ON app.product_categories(parent_id);
CREATE INDEX idx_products_business_id ON app.products(business_id);
CREATE INDEX idx_products_category_id ON app.products(category_id);
CREATE INDEX idx_products_sku ON app.products(sku);
CREATE INDEX idx_products_status ON app.products(status);
CREATE INDEX idx_product_variants_product_id ON app.product_variants(product_id);
CREATE INDEX idx_product_pricing_product_id ON app.product_pricing(product_id);
CREATE INDEX idx_product_media_product_id ON app.product_media(product_id);
CREATE INDEX idx_inventory_movements_product_id ON app.inventory_movements(product_id);
CREATE INDEX idx_inventory_movements_created_at ON app.inventory_movements(created_at);
CREATE INDEX idx_suppliers_business_id ON app.suppliers(business_id);
CREATE INDEX idx_product_suppliers_product_id ON app.product_suppliers(product_id);

-- Enable RLS
ALTER TABLE app.product_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.product_variants ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.product_pricing ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.product_media ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.inventory_movements ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.suppliers ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.product_suppliers ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Business members can manage products"
ON app.products FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);

CREATE POLICY "Product data inherits product access"
ON app.product_variants FOR ALL
USING (
    product_id IN (
        SELECT id FROM app.products 
        WHERE business_id IN (
            SELECT business_id FROM app.business_users 
            WHERE user_id = auth.uid() AND is_active = TRUE
        )
    )
);

-- Similar policies for other product-related tables
CREATE POLICY "Business members can manage categories"
ON app.product_categories FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);
```

### Product Service
```typescript
// lib/product.ts
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

export interface Product {
  id: string;
  business_id: string;
  category_id?: string;
  name: string;
  description?: string;
  sku?: string;
  barcode?: string;
  product_type: 'product' | 'service' | 'digital';
  price: number;
  cost?: number;
  currency: string;
  tax_rate: number;
  tax_inclusive: boolean;
  tax_category?: string;
  track_inventory: boolean;
  stock_quantity: number;
  low_stock_threshold: number;
  allow_backorder: boolean;
  weight?: number;
  weight_unit: string;
  dimensions?: any;
  status: 'active' | 'inactive' | 'archived';
  is_featured: boolean;
  tags?: string[];
  notes?: string;
  created_at: string;
  updated_at: string;
  category?: ProductCategory;
  variants?: ProductVariant[];
  media?: ProductMedia[];
}

export interface ProductCategory {
  id: string;
  business_id: string;
  parent_id?: string;
  name: string;
  description?: string;
  slug: string;
  sort_order: number;
  is_active: boolean;
  children?: ProductCategory[];
}

export interface ProductVariant {
  id: string;
  product_id: string;
  name: string;
  sku?: string;
  barcode?: string;
  price?: number;
  cost?: number;
  stock_quantity: number;
  attributes: Record<string, any>;
  is_active: boolean;
}

export interface ProductMedia {
  id: string;
  product_id: string;
  variant_id?: string;
  media_type: 'image' | 'video' | 'document';
  file_name: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  alt_text?: string;
  sort_order: number;
  is_primary: boolean;
}

export interface Supplier {
  id: string;
  business_id: string;
  name: string;
  contact_person?: string;
  email?: string;
  phone?: string;
  website?: string;
  address?: any;
  payment_terms: number;
  currency: string;
  status: 'active' | 'inactive';
  notes?: string;
}

export class ProductService {
  private supabase = createClientComponentClient();

  async createProduct(businessId: string, productData: Partial<Product>): Promise<Product> {
    const { data, error } = await this.supabase
      .from('products')
      .insert({
        business_id: businessId,
        ...productData,
        created_by: (await this.supabase.auth.getUser()).data.user?.id,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async updateProduct(productId: string, updates: Partial<Product>): Promise<Product> {
    const { data, error } = await this.supabase
      .from('products')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', productId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getProduct(productId: string): Promise<Product> {
    const { data, error } = await this.supabase
      .from('products')
      .select(`
        *,
        category:product_categories(*),
        variants:product_variants(*),
        media:product_media(*)
      `)
      .eq('id', productId)
      .single();

    if (error) throw error;
    return data;
  }

  async getProducts(
    businessId: string,
    filters?: {
      category_id?: string;
      status?: string;
      product_type?: string;
      search?: string;
      tags?: string[];
      low_stock?: boolean;
    },
    pagination?: { page: number; limit: number }
  ): Promise<{ products: Product[]; total: number }> {
    let query = this.supabase
      .from('products')
      .select(`
        *,
        category:product_categories(name),
        variants:product_variants(*),
        media:product_media(*)
      `, { count: 'exact' })
      .eq('business_id', businessId);

    // Apply filters
    if (filters?.category_id) {
      query = query.eq('category_id', filters.category_id);
    }
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }
    if (filters?.product_type) {
      query = query.eq('product_type', filters.product_type);
    }
    if (filters?.search) {
      query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%,sku.ilike.%${filters.search}%`);
    }
    if (filters?.tags && filters.tags.length > 0) {
      query = query.overlaps('tags', filters.tags);
    }
    if (filters?.low_stock) {
      query = query.filter('track_inventory', 'eq', true)
                  .filter('stock_quantity', 'lte', 'low_stock_threshold');
    }

    // Apply pagination
    if (pagination) {
      const offset = (pagination.page - 1) * pagination.limit;
      query = query.range(offset, offset + pagination.limit - 1);
    }

    query = query.order('name');

    const { data, error, count } = await query;
    if (error) throw error;

    return {
      products: data || [],
      total: count || 0,
    };
  }

  async createCategory(businessId: string, categoryData: Partial<ProductCategory>): Promise<ProductCategory> {
    const { data, error } = await this.supabase
      .from('product_categories')
      .insert({
        business_id: businessId,
        ...categoryData,
        slug: this.generateSlug(categoryData.name!),
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getCategories(businessId: string): Promise<ProductCategory[]> {
    const { data, error } = await this.supabase
      .from('product_categories')
      .select('*')
      .eq('business_id', businessId)
      .order('sort_order');

    if (error) throw error;
    return this.buildCategoryTree(data || []);
  }

  async addProductVariant(productId: string, variantData: Partial<ProductVariant>): Promise<ProductVariant> {
    const { data, error } = await this.supabase
      .from('product_variants')
      .insert({
        product_id: productId,
        ...variantData,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async uploadProductMedia(productId: string, file: File, variantId?: string): Promise<ProductMedia> {
    const fileExt = file.name.split('.').pop();
    const fileName = `${productId}/${Date.now()}.${fileExt}`;

    const { error: uploadError } = await this.supabase.storage
      .from('product-media')
      .upload(fileName, file);

    if (uploadError) throw uploadError;

    const { data } = this.supabase.storage
      .from('product-media')
      .getPublicUrl(fileName);

    const { data: media, error: mediaError } = await this.supabase
      .from('product_media')
      .insert({
        product_id: productId,
        variant_id: variantId,
        media_type: file.type.startsWith('image/') ? 'image' : 'document',
        file_name: file.name,
        file_path: data.publicUrl,
        file_size: file.size,
        mime_type: file.type,
        uploaded_by: (await this.supabase.auth.getUser()).data.user?.id,
      })
      .select()
      .single();

    if (mediaError) throw mediaError;
    return media;
  }

  async updateInventory(
    productId: string,
    quantity: number,
    movementType: 'in' | 'out' | 'adjustment',
    notes?: string,
    variantId?: string
  ): Promise<void> {
    // Record inventory movement
    await this.supabase
      .from('inventory_movements')
      .insert({
        product_id: productId,
        variant_id: variantId,
        movement_type: movementType,
        quantity: movementType === 'out' ? -Math.abs(quantity) : Math.abs(quantity),
        notes,
        created_by: (await this.supabase.auth.getUser()).data.user?.id,
      });

    // Update stock quantity
    const table = variantId ? 'product_variants' : 'products';
    const idField = variantId ? 'id' : 'id';
    const idValue = variantId || productId;

    if (movementType === 'adjustment') {
      await this.supabase
        .from(table)
        .update({ stock_quantity: quantity })
        .eq(idField, idValue);
    } else {
      const increment = movementType === 'in' ? quantity : -quantity;
      await this.supabase.rpc('increment_stock', {
        table_name: table,
        id_value: idValue,
        increment_value: increment,
      });
    }
  }

  async getInventoryMovements(productId: string, limit = 50): Promise<any[]> {
    const { data, error } = await this.supabase
      .from('inventory_movements')
      .select(`
        *,
        created_by_user:users(first_name, last_name)
      `)
      .eq('product_id', productId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  }

  async getLowStockProducts(businessId: string): Promise<Product[]> {
    const { data, error } = await this.supabase
      .from('products')
      .select('*')
      .eq('business_id', businessId)
      .eq('track_inventory', true)
      .filter('stock_quantity', 'lte', 'low_stock_threshold')
      .eq('status', 'active');

    if (error) throw error;
    return data || [];
  }

  async importProducts(businessId: string, products: Partial<Product>[]): Promise<{
    imported: number;
    errors: { row: number; error: string }[];
  }> {
    const results = {
      imported: 0,
      errors: [] as { row: number; error: string }[],
    };

    for (let i = 0; i < products.length; i++) {
      try {
        await this.createProduct(businessId, products[i]);
        results.imported++;
      } catch (error) {
        results.errors.push({
          row: i + 1,
          error: error.message,
        });
      }
    }

    return results;
  }

  async getProductAnalytics(productId: string): Promise<{
    totalSold: number;
    totalRevenue: number;
    averagePrice: number;
    lastSoldDate?: string;
    topCustomers: any[];
  }> {
    const { data, error } = await this.supabase.rpc('get_product_analytics', {
      p_product_id: productId,
    });

    if (error) throw error;
    return data || {
      totalSold: 0,
      totalRevenue: 0,
      averagePrice: 0,
      topCustomers: [],
    };
  }

  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  }

  private buildCategoryTree(categories: ProductCategory[]): ProductCategory[] {
    const categoryMap = new Map<string, ProductCategory>();
    const rootCategories: ProductCategory[] = [];

    // First pass: create map and initialize children arrays
    categories.forEach(category => {
      categoryMap.set(category.id, { ...category, children: [] });
    });

    // Second pass: build tree structure
    categories.forEach(category => {
      const categoryWithChildren = categoryMap.get(category.id)!;
      
      if (category.parent_id) {
        const parent = categoryMap.get(category.parent_id);
        if (parent) {
          parent.children!.push(categoryWithChildren);
        }
      } else {
        rootCategories.push(categoryWithChildren);
      }
    });

    return rootCategories;
  }
}
```

## Security Measures
- **Business Isolation**: Complete product data isolation between businesses
- **Inventory Security**: Secure inventory movement tracking
- **Media Security**: Secure file upload and access control
- **SKU Uniqueness**: Unique SKU enforcement per business
- **Price Validation**: Positive price and cost validation
- **Access Control**: Role-based permissions for product management

## Integration Points
- **Business Management**: Business-scoped product data
- **Invoice Management**: Product references in invoice line items
- **Tax Management**: Product-specific tax rates and categories
- **Document Management**: Product media and document storage
- **Reporting & Analytics**: Product performance metrics

## Error Handling & Validation
- **SKU Validation**: Unique SKU enforcement and format validation
- **Price Validation**: Positive values and currency consistency
- **Inventory Validation**: Stock quantity and movement validation
- **Category Validation**: Valid category hierarchy
- **Media Validation**: File type and size validation

## Testing Strategy
- **Unit Tests**: Product service methods and calculations
- **Integration Tests**: Database operations and RLS policies
- **E2E Tests**: Complete product management workflows
- **Inventory Tests**: Stock movement accuracy and consistency
- **Performance Tests**: Large product catalog handling

## Implementation Tasks
1. **Database Schema Setup**
   - Create product-related tables and indexes
   - Implement RLS policies for data isolation
   - Set up inventory tracking system

2. **Product Service Development**
   - Build ProductService with all CRUD operations
   - Implement inventory management system
   - Create category management functionality

3. **Frontend Components**
   - Build product list and detail views
   - Create product creation and editing forms
   - Implement category management interface

4. **Media Management**
   - Build file upload and management system
   - Create image gallery components
   - Implement media optimization

5. **Inventory System**
   - Build inventory tracking and movements
   - Create low stock alerts and notifications
   - Implement inventory reports

6. **Advanced Features**
   - Build product variants system
   - Implement bulk operations
   - Create product analytics dashboard

## Dependencies
- **Business Management**: Business context and user permissions
- **Document Management**: Product media storage
- **Tax Management**: Product tax configuration
- **Invoice Management**: Product references in invoices

## Success Criteria
- ✅ Products can be created and managed completely
- ✅ Category hierarchy works properly
- ✅ Inventory tracking is accurate and reliable
- ✅ Product variants can be managed effectively
- ✅ Media upload and management works seamlessly
- ✅ Product search and filtering is fast and accurate
- ✅ Import/export functionality works reliably
- ✅ Product analytics provide valuable insights
