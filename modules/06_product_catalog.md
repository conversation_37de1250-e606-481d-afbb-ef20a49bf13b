# Product & Service Catalog

## Overview
This module manages the complete product and service catalog for businesses, including inventory tracking, pricing management, categories, and product variants. It provides a flexible foundation for invoice line items and supports both physical products and services with comprehensive metadata and analytics.

## Core Functionalities
- Product and service management with detailed information
- Category and subcategory organization
- Multiple pricing tiers and currency support
- Product variants and options (size, color, etc.)
- Inventory tracking and stock management
- Product images and document attachments
- Bulk import/export capabilities
- Product analytics and performance tracking
- Tax configuration per product
- Supplier and vendor management

## Technical Specifications

### Database Schema
```sql
-- Product categories for organization
CREATE TABLE app.product_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES app.product_categories(id),
    
    name TEXT NOT NULL,
    description TEXT,
    slug TEXT NOT NULL,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, slug)
);

-- Main products table
CREATE TABLE app.products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    category_id UUID REFERENCES app.product_categories(id),
    
    -- Basic information
    name TEXT NOT NULL,
    description TEXT,
    sku TEXT,
    barcode TEXT,
    product_type TEXT NOT NULL DEFAULT 'product' CHECK (product_type IN ('product', 'service', 'digital')),
    
    -- Pricing
    price NUMERIC(12, 2) NOT NULL CHECK (price >= 0),
    cost NUMERIC(12, 2) CHECK (cost >= 0),
    currency TEXT NOT NULL DEFAULT 'USD',
    
    -- Tax information
    tax_rate NUMERIC(5, 4) DEFAULT 0,
    tax_inclusive BOOLEAN DEFAULT FALSE,
    tax_category TEXT,
    
    -- Inventory (for physical products)
    track_inventory BOOLEAN DEFAULT FALSE,
    stock_quantity INTEGER DEFAULT 0,
    low_stock_threshold INTEGER DEFAULT 0,
    allow_backorder BOOLEAN DEFAULT FALSE,
    
    -- Physical attributes
    weight NUMERIC(8, 3),
    weight_unit TEXT DEFAULT 'kg',
    dimensions JSONB, -- {length, width, height, unit}
    
    -- Status and visibility
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'archived')),
    is_featured BOOLEAN DEFAULT FALSE,
    
    -- Metadata
    tags TEXT[],
    notes TEXT,
    
    -- Tracking
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, sku) WHERE sku IS NOT NULL
);

-- Product variants (for products with options like size, color)
CREATE TABLE app.product_variants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES app.products(id) ON DELETE CASCADE,
    
    name TEXT NOT NULL,
    sku TEXT,
    barcode TEXT,
    
    -- Pricing (can override parent product)
    price NUMERIC(12, 2),
    cost NUMERIC(12, 2),
    
    -- Inventory
    stock_quantity INTEGER DEFAULT 0,
    
    -- Variant attributes
    attributes JSONB NOT NULL, -- {color: "red", size: "large"}
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(product_id, sku) WHERE sku IS NOT NULL
);

-- Product pricing tiers for different customer types
CREATE TABLE app.product_pricing (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES app.products(id) ON DELETE CASCADE,
    variant_id UUID REFERENCES app.product_variants(id),
    
    tier_name TEXT NOT NULL,
    min_quantity INTEGER DEFAULT 1,
    price NUMERIC(12, 2) NOT NULL,
    currency TEXT NOT NULL DEFAULT 'USD',
    
    valid_from TIMESTAMPTZ,
    valid_until TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(product_id, variant_id, tier_name, min_quantity)
);

-- Product images and media
CREATE TABLE app.product_media (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES app.products(id) ON DELETE CASCADE,
    variant_id UUID REFERENCES app.product_variants(id),
    
    media_type TEXT NOT NULL CHECK (media_type IN ('image', 'video', 'document')),
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type TEXT NOT NULL,
    
    alt_text TEXT,
    sort_order INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT FALSE,
    
    uploaded_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Inventory movements for tracking stock changes
CREATE TABLE app.inventory_movements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES app.products(id) ON DELETE CASCADE,
    variant_id UUID REFERENCES app.product_variants(id),
    
    movement_type TEXT NOT NULL CHECK (movement_type IN ('in', 'out', 'adjustment', 'transfer')),
    quantity INTEGER NOT NULL,
    reference_type TEXT, -- 'invoice', 'purchase', 'adjustment', etc.
    reference_id UUID,
    
    notes TEXT,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Suppliers/vendors for products
CREATE TABLE app.suppliers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    name TEXT NOT NULL,
    contact_person TEXT,
    email TEXT,
    phone TEXT,
    website TEXT,
    
    address JSONB,
    
    payment_terms INTEGER DEFAULT 30,
    currency TEXT DEFAULT 'USD',
    
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    notes TEXT,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Product-supplier relationships
CREATE TABLE app.product_suppliers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES app.products(id) ON DELETE CASCADE,
    supplier_id UUID NOT NULL REFERENCES app.suppliers(id) ON DELETE CASCADE,
    
    supplier_sku TEXT,
    supplier_price NUMERIC(12, 2),
    lead_time_days INTEGER,
    minimum_order_quantity INTEGER DEFAULT 1,
    
    is_primary BOOLEAN DEFAULT FALSE,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(product_id, supplier_id)
);

-- Indexes for performance
CREATE INDEX idx_product_categories_business_id ON app.product_categories(business_id);
CREATE INDEX idx_product_categories_parent_id ON app.product_categories(parent_id);
CREATE INDEX idx_products_business_id ON app.products(business_id);
CREATE INDEX idx_products_category_id ON app.products(category_id);
CREATE INDEX idx_products_sku ON app.products(sku);
CREATE INDEX idx_products_status ON app.products(status);
CREATE INDEX idx_product_variants_product_id ON app.product_variants(product_id);
CREATE INDEX idx_product_pricing_product_id ON app.product_pricing(product_id);
CREATE INDEX idx_product_media_product_id ON app.product_media(product_id);
CREATE INDEX idx_inventory_movements_product_id ON app.inventory_movements(product_id);
CREATE INDEX idx_inventory_movements_created_at ON app.inventory_movements(created_at);
CREATE INDEX idx_suppliers_business_id ON app.suppliers(business_id);
CREATE INDEX idx_product_suppliers_product_id ON app.product_suppliers(product_id);

-- Enable RLS
ALTER TABLE app.product_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.product_variants ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.product_pricing ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.product_media ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.inventory_movements ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.suppliers ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.product_suppliers ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Business members can manage products"
ON app.products FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);

CREATE POLICY "Product data inherits product access"
ON app.product_variants FOR ALL
USING (
    product_id IN (
        SELECT id FROM app.products 
        WHERE business_id IN (
            SELECT business_id FROM app.business_users 
            WHERE user_id = auth.uid() AND is_active = TRUE
        )
    )
);

-- Similar policies for other product-related tables
CREATE POLICY "Business members can manage categories"
ON app.product_categories FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);
```

### Product Service Architecture

**Core Service: ProductService**
- **Primary Responsibilities**: Product catalog management, inventory tracking, category organization, supplier management
- **Key Methods**:
  - `createProduct(businessId, productData)` - Product creation with comprehensive metadata
  - `updateProduct(productId, updates)` - Product information and pricing management
  - `getProduct(productId)` - Detailed product retrieval with variants and media
  - `getProducts(businessId, filters, pagination)` - Advanced product listing and filtering
  - `createCategory(businessId, categoryData)` - Hierarchical category management
  - `getCategories(businessId)` - Category tree structure retrieval
  - `addProductVariant(productId, variantData)` - Product variant management
  - `uploadProductMedia(productId, file, variantId)` - Product image and media management
  - `updateInventory(productId, quantity, movementType, notes)` - Inventory tracking and management
  - `getInventoryMovements(productId, limit)` - Inventory history and audit trail
  - `getLowStockProducts(businessId)` - Stock level monitoring and alerts
  - `importProducts(businessId, products)` - Bulk product import with validation
  - `getProductAnalytics(productId)` - Product performance and sales analytics

**Implementation Approach**:
- Build flexible product system supporting physical products, services, and digital goods
- Implement hierarchical category system with unlimited nesting
- Create comprehensive variant system for product options (size, color, etc.)
- Establish inventory tracking with movement history and automated alerts
- Build supplier management with purchase order integration

### Product Data Management Strategy

**Product Catalog System**:
- **Multi-Type Support**: Physical products, services, and digital goods with type-specific features
- **Variant Management**: Product variants with individual SKUs, pricing, and inventory
- **Category Hierarchy**: Unlimited category nesting with slug-based URLs
- **Media Management**: Multiple images, videos, and documents per product
- **Pricing Flexibility**: Multiple pricing tiers and currency support

**Inventory Management**:
- **Real-Time Tracking**: Live inventory updates with movement history
- **Movement Types**: Stock in, out, adjustments, and transfers with full audit trail
- **Alert System**: Low stock alerts and automated reorder notifications
- **Backorder Support**: Allow sales when out of stock with customer notification
- **Multi-Location**: Support for multiple warehouses and locations

**Supplier Integration**:
- **Supplier Profiles**: Comprehensive supplier information and contact management
- **Product Sourcing**: Link products to suppliers with pricing and lead times
- **Purchase Orders**: Integration with procurement workflows
- **Performance Tracking**: Supplier performance metrics and evaluation

## Security Measures
- **Business Isolation**: Complete product data isolation between businesses
- **Inventory Security**: Secure inventory movement tracking
- **Media Security**: Secure file upload and access control
- **SKU Uniqueness**: Unique SKU enforcement per business
- **Price Validation**: Positive price and cost validation
- **Access Control**: Role-based permissions for product management

## Integration Points
- **Business Management**: Business-scoped product data
- **Invoice Management**: Product references in invoice line items
- **Tax Management**: Product-specific tax rates and categories
- **Document Management**: Product media and document storage
- **Reporting & Analytics**: Product performance metrics

## Error Handling & Validation
- **SKU Validation**: Unique SKU enforcement and format validation
- **Price Validation**: Positive values and currency consistency
- **Inventory Validation**: Stock quantity and movement validation
- **Category Validation**: Valid category hierarchy
- **Media Validation**: File type and size validation

## Testing Strategy
- **Unit Tests**: Product service methods and calculations
- **Integration Tests**: Database operations and RLS policies
- **E2E Tests**: Complete product management workflows
- **Inventory Tests**: Stock movement accuracy and consistency
- **Performance Tests**: Large product catalog handling

## Implementation Tasks
1. **Database Schema Setup**
   - Create product-related tables and indexes
   - Implement RLS policies for data isolation
   - Set up inventory tracking system

2. **Product Service Development**
   - Build ProductService with all CRUD operations
   - Implement inventory management system
   - Create category management functionality

3. **Frontend Components**
   - Build product list and detail views
   - Create product creation and editing forms
   - Implement category management interface

4. **Media Management**
   - Build file upload and management system
   - Create image gallery components
   - Implement media optimization

5. **Inventory System**
   - Build inventory tracking and movements
   - Create low stock alerts and notifications
   - Implement inventory reports

6. **Advanced Features**
   - Build product variants system
   - Implement bulk operations
   - Create product analytics dashboard

## Dependencies
- **Business Management**: Business context and user permissions
- **Document Management**: Product media storage
- **Tax Management**: Product tax configuration
- **Invoice Management**: Product references in invoices

## Success Criteria
- ✅ Products can be created and managed completely
- ✅ Category hierarchy works properly
- ✅ Inventory tracking is accurate and reliable
- ✅ Product variants can be managed effectively
- ✅ Media upload and management works seamlessly
- ✅ Product search and filtering is fast and accurate
- ✅ Import/export functionality works reliably
- ✅ Product analytics provide valuable insights
