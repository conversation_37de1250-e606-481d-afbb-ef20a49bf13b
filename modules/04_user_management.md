# User Management & Roles

## Overview
This module manages user profiles, preferences, role assignments, and user-related functionality within the multi-tenant business context. It provides comprehensive user administration capabilities while maintaining security and data privacy across different businesses.

## Core Functionalities
- User profile management and preferences
- Avatar upload and management
- User activity tracking and session management
- Role assignment and permission management
- User onboarding and help system
- User preferences and notification settings
- User search and filtering within businesses
- User deactivation and data retention policies

## Technical Specifications

### Database Schema Extensions
```sql
-- User preferences and settings
CREATE TABLE app.user_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES app.users(id) ON DELETE CASCADE,
    business_id UUID REFERENCES app.businesses(id) ON DELETE CASCADE,
    category TEXT NOT NULL,
    key TEXT NOT NULL,
    value JSONB NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(user_id, business_id, category, key)
);

-- User activity tracking
CREATE TABLE app.user_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES app.users(id) ON DELETE CASCADE,
    business_id UUID REFERENCES app.businesses(id) ON DELETE CASCADE,
    activity_type TEXT NOT NULL,
    activity_description TEXT,
    activity_data JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- User notifications
CREATE TABLE app.user_notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES app.users(id) ON DELETE CASCADE,
    business_id UUID REFERENCES app.businesses(id) ON DELETE CASCADE,
    type TEXT NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    data JSONB,
    read_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- User onboarding progress
CREATE TABLE app.user_onboarding (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES app.users(id) ON DELETE CASCADE,
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    step_name TEXT NOT NULL,
    completed_at TIMESTAMPTZ,
    data JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(user_id, business_id, step_name)
);

-- Indexes
CREATE INDEX idx_user_preferences_user_business ON app.user_preferences(user_id, business_id);
CREATE INDEX idx_user_activities_user_id ON app.user_activities(user_id);
CREATE INDEX idx_user_activities_business_id ON app.user_activities(business_id);
CREATE INDEX idx_user_activities_created_at ON app.user_activities(created_at);
CREATE INDEX idx_user_notifications_user_id ON app.user_notifications(user_id);
CREATE INDEX idx_user_notifications_unread ON app.user_notifications(user_id, read_at) WHERE read_at IS NULL;
CREATE INDEX idx_user_onboarding_user_business ON app.user_onboarding(user_id, business_id);

-- Enable RLS
ALTER TABLE app.user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.user_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.user_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.user_onboarding ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can manage their own preferences"
ON app.user_preferences FOR ALL
USING (user_id = auth.uid());

CREATE POLICY "Users can view their own activities"
ON app.user_activities FOR SELECT
USING (user_id = auth.uid());

CREATE POLICY "Business admins can view user activities"
ON app.user_activities FOR SELECT
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() 
        AND role IN ('owner', 'admin')
        AND is_active = TRUE
    )
);

CREATE POLICY "Users can manage their own notifications"
ON app.user_notifications FOR ALL
USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own onboarding"
ON app.user_onboarding FOR ALL
USING (user_id = auth.uid());
```

### User Service
```typescript
// lib/user.ts
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

export interface UserProfile {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  avatar_url?: string;
  phone?: string;
  timezone: string;
  language: string;
  email_verified: boolean;
  last_login_at?: string;
  created_at: string;
  updated_at: string;
}

export interface UserPreference {
  category: string;
  key: string;
  value: any;
}

export interface UserNotification {
  id: string;
  type: string;
  title: string;
  message: string;
  data?: any;
  read_at?: string;
  expires_at?: string;
  created_at: string;
}

export interface UserActivity {
  id: string;
  activity_type: string;
  activity_description?: string;
  activity_data?: any;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

export class UserService {
  private supabase = createClientComponentClient();

  async updateProfile(updates: Partial<UserProfile>): Promise<UserProfile> {
    const { data, error } = await this.supabase
      .from('users')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', (await this.supabase.auth.getUser()).data.user?.id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async uploadAvatar(file: File): Promise<string> {
    const user = await this.supabase.auth.getUser();
    if (!user.data.user) throw new Error('User not authenticated');

    const fileExt = file.name.split('.').pop();
    const fileName = `${user.data.user.id}/avatar.${fileExt}`;

    const { error: uploadError } = await this.supabase.storage
      .from('avatars')
      .upload(fileName, file, { upsert: true });

    if (uploadError) throw uploadError;

    const { data } = this.supabase.storage
      .from('avatars')
      .getPublicUrl(fileName);

    // Update user profile with new avatar URL
    await this.updateProfile({ avatar_url: data.publicUrl });

    return data.publicUrl;
  }

  async getUserPreferences(businessId?: string): Promise<Record<string, any>> {
    let query = this.supabase
      .from('user_preferences')
      .select('category, key, value')
      .eq('user_id', (await this.supabase.auth.getUser()).data.user?.id);

    if (businessId) {
      query = query.eq('business_id', businessId);
    }

    const { data, error } = await query;
    if (error) throw error;

    return data.reduce((acc, pref) => {
      if (!acc[pref.category]) acc[pref.category] = {};
      acc[pref.category][pref.key] = pref.value;
      return acc;
    }, {});
  }

  async updateUserPreference(
    category: string,
    key: string,
    value: any,
    businessId?: string
  ): Promise<void> {
    const user = await this.supabase.auth.getUser();
    if (!user.data.user) throw new Error('User not authenticated');

    const { error } = await this.supabase
      .from('user_preferences')
      .upsert({
        user_id: user.data.user.id,
        business_id: businessId,
        category,
        key,
        value,
        updated_at: new Date().toISOString(),
      });

    if (error) throw error;
  }

  async getNotifications(limit = 50): Promise<UserNotification[]> {
    const { data, error } = await this.supabase
      .from('user_notifications')
      .select('*')
      .eq('user_id', (await this.supabase.auth.getUser()).data.user?.id)
      .or('expires_at.is.null,expires_at.gt.' + new Date().toISOString())
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data;
  }

  async markNotificationAsRead(notificationId: string): Promise<void> {
    const { error } = await this.supabase
      .from('user_notifications')
      .update({ read_at: new Date().toISOString() })
      .eq('id', notificationId);

    if (error) throw error;
  }

  async markAllNotificationsAsRead(): Promise<void> {
    const user = await this.supabase.auth.getUser();
    if (!user.data.user) throw new Error('User not authenticated');

    const { error } = await this.supabase
      .from('user_notifications')
      .update({ read_at: new Date().toISOString() })
      .eq('user_id', user.data.user.id)
      .is('read_at', null);

    if (error) throw error;
  }

  async createNotification(
    userId: string,
    type: string,
    title: string,
    message: string,
    data?: any,
    businessId?: string,
    expiresAt?: Date
  ): Promise<void> {
    const { error } = await this.supabase
      .from('user_notifications')
      .insert({
        user_id: userId,
        business_id: businessId,
        type,
        title,
        message,
        data,
        expires_at: expiresAt?.toISOString(),
      });

    if (error) throw error;
  }

  async getUserActivities(limit = 100): Promise<UserActivity[]> {
    const { data, error } = await this.supabase
      .from('user_activities')
      .select('*')
      .eq('user_id', (await this.supabase.auth.getUser()).data.user?.id)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data;
  }

  async logActivity(
    activityType: string,
    description?: string,
    data?: any,
    businessId?: string
  ): Promise<void> {
    const user = await this.supabase.auth.getUser();
    if (!user.data.user) return; // Don't throw error for logging

    try {
      await this.supabase
        .from('user_activities')
        .insert({
          user_id: user.data.user.id,
          business_id: businessId,
          activity_type: activityType,
          activity_description: description,
          activity_data: data,
          ip_address: await this.getClientIP(),
          user_agent: navigator.userAgent,
        });
    } catch (error) {
      console.error('Failed to log activity:', error);
    }
  }

  async getOnboardingProgress(businessId: string): Promise<Record<string, any>> {
    const { data, error } = await this.supabase
      .from('user_onboarding')
      .select('step_name, completed_at, data')
      .eq('user_id', (await this.supabase.auth.getUser()).data.user?.id)
      .eq('business_id', businessId);

    if (error) throw error;

    return data.reduce((acc, step) => {
      acc[step.step_name] = {
        completed: !!step.completed_at,
        completed_at: step.completed_at,
        data: step.data,
      };
      return acc;
    }, {});
  }

  async completeOnboardingStep(
    businessId: string,
    stepName: string,
    data?: any
  ): Promise<void> {
    const user = await this.supabase.auth.getUser();
    if (!user.data.user) throw new Error('User not authenticated');

    const { error } = await this.supabase
      .from('user_onboarding')
      .upsert({
        user_id: user.data.user.id,
        business_id: businessId,
        step_name: stepName,
        completed_at: new Date().toISOString(),
        data,
      });

    if (error) throw error;
  }

  async searchUsers(businessId: string, query: string): Promise<UserProfile[]> {
    const { data, error } = await this.supabase
      .from('business_users')
      .select(`
        user:users(*)
      `)
      .eq('business_id', businessId)
      .eq('is_active', true)
      .or(`user.first_name.ilike.%${query}%,user.last_name.ilike.%${query}%,user.email.ilike.%${query}%`);

    if (error) throw error;
    return data.map(item => item.user).filter(Boolean);
  }

  private async getClientIP(): Promise<string> {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch {
      return 'unknown';
    }
  }
}
```

### React Hooks for User Management
```typescript
// hooks/useUser.ts
import { useEffect, useState } from 'react';
import { UserService, UserProfile, UserNotification } from '@/lib/user';
import { useAuth } from './useAuth';

export function useUser() {
  const { user } = useAuth();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const userService = new UserService();

  useEffect(() => {
    if (user) {
      loadProfile();
    }
  }, [user]);

  const loadProfile = async () => {
    try {
      // Profile is already available from auth, but we might need extended data
      setProfile(user as UserProfile);
    } catch (error) {
      console.error('Failed to load profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (updates: Partial<UserProfile>) => {
    const updatedProfile = await userService.updateProfile(updates);
    setProfile(updatedProfile);
    return updatedProfile;
  };

  const uploadAvatar = async (file: File) => {
    const avatarUrl = await userService.uploadAvatar(file);
    setProfile(prev => prev ? { ...prev, avatar_url: avatarUrl } : null);
    return avatarUrl;
  };

  return {
    profile,
    loading,
    updateProfile,
    uploadAvatar,
    refreshProfile: loadProfile,
  };
}

// hooks/useNotifications.ts
export function useNotifications() {
  const [notifications, setNotifications] = useState<UserNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const userService = new UserService();

  useEffect(() => {
    loadNotifications();
  }, []);

  const loadNotifications = async () => {
    try {
      const data = await userService.getNotifications();
      setNotifications(data);
      setUnreadCount(data.filter(n => !n.read_at).length);
    } catch (error) {
      console.error('Failed to load notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (notificationId: string) => {
    await userService.markNotificationAsRead(notificationId);
    setNotifications(prev =>
      prev.map(n =>
        n.id === notificationId
          ? { ...n, read_at: new Date().toISOString() }
          : n
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const markAllAsRead = async () => {
    await userService.markAllNotificationsAsRead();
    setNotifications(prev =>
      prev.map(n => ({ ...n, read_at: new Date().toISOString() }))
    );
    setUnreadCount(0);
  };

  return {
    notifications,
    unreadCount,
    loading,
    markAsRead,
    markAllAsRead,
    refreshNotifications: loadNotifications,
  };
}
```

## Security Measures
- **Profile Privacy**: Users can only access their own profile data
- **Activity Logging**: Comprehensive tracking of user actions
- **Notification Security**: Users can only see their own notifications
- **Avatar Upload Security**: File type validation and size limits
- **Data Validation**: Input sanitization and validation
- **Business Context**: All user data scoped to appropriate business

## Integration Points
- **Authentication & Authorization**: User identity and permissions
- **Business Management**: Business-scoped user data and roles
- **All Modules**: User context and activity tracking
- **Audit Logging**: User activity and change tracking

## Error Handling & Validation
- **Profile Validation**: Email format, name requirements
- **File Upload Validation**: Avatar file type and size limits
- **Preference Validation**: Valid preference values and types
- **Activity Logging**: Graceful failure handling for logging
- **Notification Delivery**: Retry logic for failed notifications

## Testing Strategy
- **Unit Tests**: User service methods and utilities
- **Integration Tests**: Profile updates and preference management
- **E2E Tests**: Complete user onboarding and profile management
- **Security Tests**: Profile access and data isolation
- **Performance Tests**: Notification and activity query performance

## Implementation Tasks
1. **Database Schema Setup**
   - Create user-related tables and indexes
   - Implement RLS policies for user data
   - Set up notification and activity tracking

2. **User Service Development**
   - Implement UserService class with all methods
   - Build avatar upload functionality
   - Create notification and activity systems

3. **Frontend Integration**
   - Build user profile management UI
   - Create notification center component
   - Implement user preferences interface

4. **Onboarding System**
   - Create onboarding flow and progress tracking
   - Build help and tutorial system
   - Implement user guidance features

5. **Activity and Analytics**
   - Implement comprehensive activity logging
   - Build user activity dashboard
   - Create user analytics and insights

## Dependencies
- **Core Infrastructure**: Database foundation and file storage
- **Authentication & Authorization**: User identity and session management
- **Business Management**: Business context and role management
- **Email Service**: Notification email delivery

## Success Criteria
- ✅ Users can manage their profiles completely
- ✅ Avatar upload and management works seamlessly
- ✅ Notification system delivers and tracks notifications
- ✅ User preferences are saved and applied correctly
- ✅ Activity logging captures all user actions
- ✅ Onboarding system guides new users effectively
- ✅ User search and filtering works within business context
