# User Management & Roles

## Overview
This module manages user profiles, preferences, role assignments, and user-related functionality within the multi-tenant business context. It provides comprehensive user administration capabilities while maintaining security and data privacy across different businesses.

## Core Functionalities
- User profile management and preferences
- Avatar upload and management
- User activity tracking and session management
- Role assignment and permission management
- User onboarding and help system
- User preferences and notification settings
- User search and filtering within businesses
- User deactivation and data retention policies

## Technical Specifications

### Database Schema Extensions
```sql
-- User preferences and settings
CREATE TABLE app.user_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES app.users(id) ON DELETE CASCADE,
    business_id UUID REFERENCES app.businesses(id) ON DELETE CASCADE,
    category TEXT NOT NULL,
    key TEXT NOT NULL,
    value JSONB NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(user_id, business_id, category, key)
);

-- User activity tracking
CREATE TABLE app.user_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES app.users(id) ON DELETE CASCADE,
    business_id UUID REFERENCES app.businesses(id) ON DELETE CASCADE,
    activity_type TEXT NOT NULL,
    activity_description TEXT,
    activity_data JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- User notifications
CREATE TABLE app.user_notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES app.users(id) ON DELETE CASCADE,
    business_id UUID REFERENCES app.businesses(id) ON DELETE CASCADE,
    type TEXT NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    data JSONB,
    read_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- User onboarding progress
CREATE TABLE app.user_onboarding (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES app.users(id) ON DELETE CASCADE,
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    step_name TEXT NOT NULL,
    completed_at TIMESTAMPTZ,
    data JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(user_id, business_id, step_name)
);

-- Indexes
CREATE INDEX idx_user_preferences_user_business ON app.user_preferences(user_id, business_id);
CREATE INDEX idx_user_activities_user_id ON app.user_activities(user_id);
CREATE INDEX idx_user_activities_business_id ON app.user_activities(business_id);
CREATE INDEX idx_user_activities_created_at ON app.user_activities(created_at);
CREATE INDEX idx_user_notifications_user_id ON app.user_notifications(user_id);
CREATE INDEX idx_user_notifications_unread ON app.user_notifications(user_id, read_at) WHERE read_at IS NULL;
CREATE INDEX idx_user_onboarding_user_business ON app.user_onboarding(user_id, business_id);

-- Enable RLS
ALTER TABLE app.user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.user_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.user_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.user_onboarding ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can manage their own preferences"
ON app.user_preferences FOR ALL
USING (user_id = auth.uid());

CREATE POLICY "Users can view their own activities"
ON app.user_activities FOR SELECT
USING (user_id = auth.uid());

CREATE POLICY "Business admins can view user activities"
ON app.user_activities FOR SELECT
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() 
        AND role IN ('owner', 'admin')
        AND is_active = TRUE
    )
);

CREATE POLICY "Users can manage their own notifications"
ON app.user_notifications FOR ALL
USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own onboarding"
ON app.user_onboarding FOR ALL
USING (user_id = auth.uid());
```

### User Service Architecture

**Core Service: UserService**
- **Primary Responsibilities**: User profile management, preferences, notifications, activity tracking
- **Key Methods**:
  - `updateProfile(updates)` - User profile information management
  - `uploadAvatar(file)` - Avatar image upload and management
  - `getUserPreferences(businessId)` - User preference retrieval and management
  - `updateUserPreference(category, key, value, businessId)` - Individual preference updates
  - `getNotifications(limit)` - User notification retrieval with pagination
  - `markNotificationAsRead(notificationId)` - Notification status management
  - `createNotification(userId, type, title, message, data)` - System notification creation
  - `getUserActivities(limit)` - User activity history retrieval
  - `logActivity(activityType, description, data, businessId)` - Activity tracking
  - `getOnboardingProgress(businessId)` - User onboarding status tracking
  - `completeOnboardingStep(businessId, stepName, data)` - Onboarding progression
  - `searchUsers(businessId, query)` - User search within business context

**Implementation Approach**:
- Build comprehensive user preference system with business-specific overrides
- Implement real-time notification system with multiple delivery channels
- Create detailed activity logging for audit and analytics purposes
- Establish user onboarding workflow with progress tracking
- Build avatar management with image optimization and CDN integration

### User Data Management Strategy

**Profile Management**:
- **Personal Information**: Name, email, phone, timezone, language preferences
- **Avatar System**: Image upload, optimization, and CDN delivery
- **Preference Hierarchy**: Global user preferences with business-specific overrides
- **Privacy Controls**: User data visibility and sharing preferences

**Notification System**:
- **Multi-Channel Delivery**: In-app, email, SMS, and push notifications
- **Notification Types**: System alerts, business updates, security notifications
- **Preference Management**: Granular control over notification preferences
- **Delivery Tracking**: Read receipts and engagement analytics

**Activity Tracking**:
- **Comprehensive Logging**: All user actions tracked for audit and analytics
- **Business Context**: Activity scoped to specific business environments
- **Privacy Compliance**: GDPR-compliant activity data management
- **Analytics Integration**: Activity data feeding into business intelligence

### Frontend Integration Strategy

**User Management Hooks**:
- **useUser()**: Core user profile and preference management
- **useNotifications()**: Real-time notification management with unread counts
- **useUserActivity()**: Activity history and tracking
- **useOnboarding()**: Guided user onboarding experience

## Security Measures
- **Profile Privacy**: Users can only access their own profile data
- **Activity Logging**: Comprehensive tracking of user actions
- **Notification Security**: Users can only see their own notifications
- **Avatar Upload Security**: File type validation and size limits
- **Data Validation**: Input sanitization and validation
- **Business Context**: All user data scoped to appropriate business

## Integration Points
- **Authentication & Authorization**: User identity and permissions
- **Business Management**: Business-scoped user data and roles
- **All Modules**: User context and activity tracking
- **Audit Logging**: User activity and change tracking

## Error Handling & Validation
- **Profile Validation**: Email format, name requirements
- **File Upload Validation**: Avatar file type and size limits
- **Preference Validation**: Valid preference values and types
- **Activity Logging**: Graceful failure handling for logging
- **Notification Delivery**: Retry logic for failed notifications

## Testing Strategy
- **Unit Tests**: User service methods and utilities
- **Integration Tests**: Profile updates and preference management
- **E2E Tests**: Complete user onboarding and profile management
- **Security Tests**: Profile access and data isolation
- **Performance Tests**: Notification and activity query performance

## Implementation Tasks
1. **Database Schema Setup**
   - Create user-related tables and indexes
   - Implement RLS policies for user data
   - Set up notification and activity tracking

2. **User Service Development**
   - Implement UserService class with all methods
   - Build avatar upload functionality
   - Create notification and activity systems

3. **Frontend Integration**
   - Build user profile management UI
   - Create notification center component
   - Implement user preferences interface

4. **Onboarding System**
   - Create onboarding flow and progress tracking
   - Build help and tutorial system
   - Implement user guidance features

5. **Activity and Analytics**
   - Implement comprehensive activity logging
   - Build user activity dashboard
   - Create user analytics and insights

## Dependencies
- **Core Infrastructure**: Database foundation and file storage
- **Authentication & Authorization**: User identity and session management
- **Business Management**: Business context and role management
- **Email Service**: Notification email delivery

## Success Criteria
- ✅ Users can manage their profiles completely
- ✅ Avatar upload and management works seamlessly
- ✅ Notification system delivers and tracks notifications
- ✅ User preferences are saved and applied correctly
- ✅ Activity logging captures all user actions
- ✅ Onboarding system guides new users effectively
- ✅ User search and filtering works within business context
