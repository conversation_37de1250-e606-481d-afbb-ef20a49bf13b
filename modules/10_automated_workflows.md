# Automated Workflows & Notifications

## Overview
This module provides comprehensive workflow automation and notification capabilities, enabling businesses to automate repetitive tasks, send timely notifications, and create sophisticated business process automation. It includes email automation, payment reminders, invoice workflows, and custom trigger-based actions.

## Core Functionalities
- Automated email workflows and sequences
- Payment reminder automation
- Invoice status-based triggers
- Custom workflow builder with conditions
- Notification scheduling and delivery
- Event-driven automation triggers
- Multi-channel notifications (email, SMS, in-app)
- Workflow analytics and performance tracking
- Template-based communication
- Integration with external services

## Technical Specifications

### Database Schema
```sql
-- Workflow definitions
CREATE TABLE app.workflows (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,

    name TEXT NOT NULL,
    description TEXT,
    workflow_type TEXT NOT NULL, -- 'email_sequence', 'payment_reminder', 'custom'

    -- Trigger configuration
    trigger_event TEXT NOT NULL, -- 'invoice_created', 'payment_overdue', 'customer_created'
    trigger_conditions JSONB, -- Conditions that must be met

    -- Workflow steps
    workflow_steps JSONB NOT NULL, -- Array of workflow steps

    -- Status and settings
    is_active BOOLEAN DEFAULT TRUE,
    execution_count INTEGER DEFAULT 0,
    last_executed_at TIMESTAMPTZ,

    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Workflow executions
CREATE TABLE app.workflow_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_id UUID NOT NULL REFERENCES app.workflows(id) ON DELETE CASCADE,
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,

    -- Execution context
    trigger_data JSONB NOT NULL, -- Data that triggered the workflow
    execution_status TEXT NOT NULL DEFAULT 'running' CHECK (execution_status IN ('running', 'completed', 'failed', 'cancelled')),

    -- Progress tracking
    current_step INTEGER DEFAULT 0,
    total_steps INTEGER NOT NULL,
    completed_steps INTEGER DEFAULT 0,
    failed_steps INTEGER DEFAULT 0,

    -- Timing
    started_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    completed_at TIMESTAMPTZ,

    -- Error handling
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3
);

-- Individual workflow step executions
CREATE TABLE app.workflow_step_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_execution_id UUID NOT NULL REFERENCES app.workflow_executions(id) ON DELETE CASCADE,

    step_number INTEGER NOT NULL,
    step_type TEXT NOT NULL, -- 'email', 'delay', 'condition', 'webhook', 'update_record'
    step_config JSONB NOT NULL,

    -- Execution details
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed', 'skipped')),
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,

    -- Results
    execution_result JSONB,
    error_message TEXT,

    -- Retry logic
    retry_count INTEGER DEFAULT 0,
    next_retry_at TIMESTAMPTZ
);

-- Scheduled notifications
CREATE TABLE app.scheduled_notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    workflow_execution_id UUID REFERENCES app.workflow_executions(id) ON DELETE CASCADE,

    -- Notification details
    notification_type TEXT NOT NULL, -- 'email', 'sms', 'in_app', 'webhook'
    recipient_type TEXT NOT NULL, -- 'customer', 'user', 'external'
    recipient_id UUID,
    recipient_email TEXT,
    recipient_phone TEXT,

    -- Content
    subject TEXT,
    content TEXT NOT NULL,
    template_id UUID,
    template_data JSONB,

    -- Scheduling
    scheduled_for TIMESTAMPTZ NOT NULL,
    sent_at TIMESTAMPTZ,

    -- Status
    status TEXT NOT NULL DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'sent', 'failed', 'cancelled')),
    delivery_attempts INTEGER DEFAULT 0,
    error_message TEXT,

    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Notification delivery tracking
CREATE TABLE app.notification_deliveries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    scheduled_notification_id UUID NOT NULL REFERENCES app.scheduled_notifications(id) ON DELETE CASCADE,

    delivery_method TEXT NOT NULL,
    delivery_status TEXT NOT NULL,
    delivery_response JSONB,

    -- Tracking
    opened_at TIMESTAMPTZ,
    clicked_at TIMESTAMPTZ,
    bounced_at TIMESTAMPTZ,

    delivered_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Workflow templates for common patterns
CREATE TABLE app.workflow_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    name TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL, -- 'payment_reminders', 'customer_onboarding', 'invoice_follow_up'

    -- Template configuration
    template_config JSONB NOT NULL,
    variables JSONB, -- Available variables

    -- Metadata
    is_public BOOLEAN DEFAULT TRUE,
    usage_count INTEGER DEFAULT 0,

    created_by UUID REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_workflows_business_id ON app.workflows(business_id);
CREATE INDEX idx_workflows_trigger_event ON app.workflows(trigger_event);
CREATE INDEX idx_workflow_executions_workflow_id ON app.workflow_executions(workflow_id);
CREATE INDEX idx_workflow_executions_status ON app.workflow_executions(execution_status);
CREATE INDEX idx_workflow_step_executions_execution_id ON app.workflow_step_executions(workflow_execution_id);
CREATE INDEX idx_scheduled_notifications_scheduled_for ON app.scheduled_notifications(scheduled_for);
CREATE INDEX idx_scheduled_notifications_status ON app.scheduled_notifications(status);
CREATE INDEX idx_notification_deliveries_notification_id ON app.notification_deliveries(scheduled_notification_id);
CREATE INDEX idx_workflow_templates_category ON app.workflow_templates(category);

-- Enable RLS
ALTER TABLE app.workflows ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.workflow_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.workflow_step_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.scheduled_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.notification_deliveries ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.workflow_templates ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Business members can manage workflows"
ON app.workflows FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);

CREATE POLICY "Business members can view workflow executions"
ON app.workflow_executions FOR SELECT
USING (
    business_id IN (
        SELECT business_id FROM app.business_users
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);

CREATE POLICY "Everyone can view public workflow templates"
ON app.workflow_templates FOR SELECT
USING (is_public = true);
```

### Workflow Service
```typescript
// lib/workflow.ts
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

export interface Workflow {
  id: string;
  business_id: string;
  name: string;
  description?: string;
  workflow_type: string;
  trigger_event: string;
  trigger_conditions?: any;
  workflow_steps: WorkflowStep[];
  is_active: boolean;
  execution_count: number;
  last_executed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface WorkflowStep {
  step_number: number;
  step_type: 'email' | 'delay' | 'condition' | 'webhook' | 'update_record';
  step_config: any;
  conditions?: any;
}

export interface WorkflowExecution {
  id: string;
  workflow_id: string;
  business_id: string;
  trigger_data: any;
  execution_status: 'running' | 'completed' | 'failed' | 'cancelled';
  current_step: number;
  total_steps: number;
  completed_steps: number;
  failed_steps: number;
  started_at: string;
  completed_at?: string;
  error_message?: string;
  retry_count: number;
  max_retries: number;
}

export interface ScheduledNotification {
  id: string;
  business_id: string;
  workflow_execution_id?: string;
  notification_type: 'email' | 'sms' | 'in_app' | 'webhook';
  recipient_type: 'customer' | 'user' | 'external';
  recipient_id?: string;
  recipient_email?: string;
  recipient_phone?: string;
  subject?: string;
  content: string;
  template_id?: string;
  template_data?: any;
  scheduled_for: string;
  sent_at?: string;
  status: 'scheduled' | 'sent' | 'failed' | 'cancelled';
  delivery_attempts: number;
  error_message?: string;
}

export class WorkflowService {
  private supabase = createClientComponentClient();

  async createWorkflow(businessId: string, workflowData: Partial<Workflow>): Promise<Workflow> {
    const user = await this.supabase.auth.getUser();

    const { data, error } = await this.supabase
      .from('workflows')
      .insert({
        business_id: businessId,
        ...workflowData,
        created_by: user.data.user?.id,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async updateWorkflow(workflowId: string, updates: Partial<Workflow>): Promise<Workflow> {
    const { data, error } = await this.supabase
      .from('workflows')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', workflowId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getWorkflows(businessId: string, workflowType?: string): Promise<Workflow[]> {
    let query = this.supabase
      .from('workflows')
      .select('*')
      .eq('business_id', businessId);

    if (workflowType) {
      query = query.eq('workflow_type', workflowType);
    }

    query = query.order('name');

    const { data, error } = await query;
    if (error) throw error;

    return data || [];
  }

  async triggerWorkflow(workflowId: string, triggerData: any): Promise<WorkflowExecution> {
    const workflow = await this.getWorkflow(workflowId);
    if (!workflow || !workflow.is_active) {
      throw new Error('Workflow not found or inactive');
    }

    // Check trigger conditions
    if (!this.evaluateConditions(workflow.trigger_conditions, triggerData)) {
      throw new Error('Trigger conditions not met');
    }

    // Create workflow execution
    const { data: execution, error } = await this.supabase
      .from('workflow_executions')
      .insert({
        workflow_id: workflowId,
        business_id: workflow.business_id,
        trigger_data: triggerData,
        total_steps: workflow.workflow_steps.length,
      })
      .select()
      .single();

    if (error) throw error;

    // Start execution
    await this.executeWorkflow(execution.id);

    // Update workflow execution count
    await this.supabase
      .from('workflows')
      .update({
        execution_count: workflow.execution_count + 1,
        last_executed_at: new Date().toISOString(),
      })
      .eq('id', workflowId);

    return execution;
  }

  async executeWorkflow(executionId: string): Promise<void> {
    const execution = await this.getWorkflowExecution(executionId);
    if (!execution) throw new Error('Workflow execution not found');

    const workflow = await this.getWorkflow(execution.workflow_id);
    if (!workflow) throw new Error('Workflow not found');

    try {
      for (let i = execution.current_step; i < workflow.workflow_steps.length; i++) {
        const step = workflow.workflow_steps[i];

        // Create step execution record
        const { data: stepExecution } = await this.supabase
          .from('workflow_step_executions')
          .insert({
            workflow_execution_id: executionId,
            step_number: step.step_number,
            step_type: step.step_type,
            step_config: step.step_config,
            status: 'running',
            started_at: new Date().toISOString(),
          })
          .select()
          .single();

        try {
          // Execute the step
          const result = await this.executeWorkflowStep(step, execution.trigger_data);

          // Update step execution as completed
          await this.supabase
            .from('workflow_step_executions')
            .update({
              status: 'completed',
              completed_at: new Date().toISOString(),
              execution_result: result,
            })
            .eq('id', stepExecution.id);

          // Update workflow execution progress
          await this.supabase
            .from('workflow_executions')
            .update({
              current_step: i + 1,
              completed_steps: execution.completed_steps + 1,
            })
            .eq('id', executionId);

        } catch (stepError) {
          // Update step execution as failed
          await this.supabase
            .from('workflow_step_executions')
            .update({
              status: 'failed',
              completed_at: new Date().toISOString(),
              error_message: stepError.message,
            })
            .eq('id', stepExecution.id);

          // Update workflow execution
          await this.supabase
            .from('workflow_executions')
            .update({
              failed_steps: execution.failed_steps + 1,
            })
            .eq('id', executionId);

          // Decide whether to continue or fail the entire workflow
          if (step.step_config.continue_on_error !== true) {
            throw stepError;
          }
        }
      }

      // Mark workflow execution as completed
      await this.supabase
        .from('workflow_executions')
        .update({
          execution_status: 'completed',
          completed_at: new Date().toISOString(),
        })
        .eq('id', executionId);

    } catch (error) {
      // Mark workflow execution as failed
      await this.supabase
        .from('workflow_executions')
        .update({
          execution_status: 'failed',
          completed_at: new Date().toISOString(),
          error_message: error.message,
        })
        .eq('id', executionId);

      throw error;
    }
  }

  async scheduleNotification(notificationData: Partial<ScheduledNotification>): Promise<ScheduledNotification> {
    const { data, error } = await this.supabase
      .from('scheduled_notifications')
      .insert(notificationData)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async processScheduledNotifications(): Promise<void> {
    // Get notifications that are due to be sent
    const { data: notifications } = await this.supabase
      .from('scheduled_notifications')
      .select('*')
      .eq('status', 'scheduled')
      .lte('scheduled_for', new Date().toISOString())
      .limit(100);

    if (!notifications) return;

    for (const notification of notifications) {
      try {
        await this.sendNotification(notification);
      } catch (error) {
        console.error(`Failed to send notification ${notification.id}:`, error);
      }
    }
  }

  async createPaymentReminderWorkflow(businessId: string, config: {
    reminderDays: number[];
    emailTemplate: string;
    isActive?: boolean;
  }): Promise<Workflow> {
    const workflowSteps: WorkflowStep[] = config.reminderDays.map((days, index) => ({
      step_number: index + 1,
      step_type: 'email',
      step_config: {
        template: config.emailTemplate,
        delay_days: days,
        recipient_type: 'customer',
        subject: `Payment Reminder - ${days} days overdue`,
      },
    }));

    return this.createWorkflow(businessId, {
      name: 'Payment Reminder Workflow',
      description: 'Automated payment reminders for overdue invoices',
      workflow_type: 'payment_reminder',
      trigger_event: 'invoice_overdue',
      trigger_conditions: {
        invoice_status: 'overdue',
        days_overdue: { gte: 1 },
      },
      workflow_steps: workflowSteps,
      is_active: config.isActive ?? true,
    });
  }

  private async executeWorkflowStep(step: WorkflowStep, triggerData: any): Promise<any> {
    switch (step.step_type) {
      case 'email':
        return this.executeEmailStep(step, triggerData);
      case 'delay':
        return this.executeDelayStep(step);
      case 'condition':
        return this.executeConditionStep(step, triggerData);
      case 'webhook':
        return this.executeWebhookStep(step, triggerData);
      case 'update_record':
        return this.executeUpdateRecordStep(step, triggerData);
      default:
        throw new Error(`Unknown step type: ${step.step_type}`);
    }
  }

  private async executeEmailStep(step: WorkflowStep, triggerData: any): Promise<any> {
    const config = step.step_config;

    // Schedule the email notification
    const scheduledFor = config.delay_days
      ? new Date(Date.now() + config.delay_days * 24 * 60 * 60 * 1000)
      : new Date();

    return this.scheduleNotification({
      business_id: triggerData.business_id,
      notification_type: 'email',
      recipient_type: config.recipient_type,
      recipient_id: triggerData.customer_id || triggerData.user_id,
      recipient_email: config.recipient_email || triggerData.customer_email,
      subject: config.subject,
      content: config.content || '',
      template_id: config.template_id,
      template_data: triggerData,
      scheduled_for: scheduledFor.toISOString(),
    });
  }

  private async executeDelayStep(step: WorkflowStep): Promise<any> {
    const delayMs = step.step_config.delay_minutes * 60 * 1000;
    await new Promise(resolve => setTimeout(resolve, delayMs));
    return { delayed_for: step.step_config.delay_minutes };
  }

  private async executeConditionStep(step: WorkflowStep, triggerData: any): Promise<any> {
    const conditionMet = this.evaluateConditions(step.step_config.conditions, triggerData);
    return { condition_met: conditionMet };
  }

  private async executeWebhookStep(step: WorkflowStep, triggerData: any): Promise<any> {
    const response = await fetch(step.step_config.url, {
      method: step.step_config.method || 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...step.step_config.headers,
      },
      body: JSON.stringify({
        ...triggerData,
        ...step.step_config.payload,
      }),
    });

    if (!response.ok) {
      throw new Error(`Webhook failed: ${response.status} ${response.statusText}`);
    }

    return { status: response.status, response: await response.json() };
  }

  private async executeUpdateRecordStep(step: WorkflowStep, triggerData: any): Promise<any> {
    const config = step.step_config;

    const { error } = await this.supabase
      .from(config.table)
      .update(config.updates)
      .eq('id', triggerData[config.record_id_field]);

    if (error) throw error;
    return { updated: true };
  }

  private evaluateConditions(conditions: any, data: any): boolean {
    if (!conditions) return true;

    // Simple condition evaluation - in a real implementation,
    // you'd want a more sophisticated condition engine
    for (const [field, condition] of Object.entries(conditions)) {
      const value = data[field];

      if (typeof condition === 'object') {
        if (condition.eq && value !== condition.eq) return false;
        if (condition.ne && value === condition.ne) return false;
        if (condition.gt && value <= condition.gt) return false;
        if (condition.gte && value < condition.gte) return false;
        if (condition.lt && value >= condition.lt) return false;
        if (condition.lte && value > condition.lte) return false;
        if (condition.in && !condition.in.includes(value)) return false;
        if (condition.nin && condition.nin.includes(value)) return false;
      } else {
        if (value !== condition) return false;
      }
    }

    return true;
  }

  private async sendNotification(notification: ScheduledNotification): Promise<void> {
    try {
      let result;

      switch (notification.notification_type) {
        case 'email':
          result = await this.sendEmail(notification);
          break;
        case 'sms':
          result = await this.sendSMS(notification);
          break;
        case 'webhook':
          result = await this.sendWebhook(notification);
          break;
        default:
          throw new Error(`Unsupported notification type: ${notification.notification_type}`);
      }

      // Update notification as sent
      await this.supabase
        .from('scheduled_notifications')
        .update({
          status: 'sent',
          sent_at: new Date().toISOString(),
          delivery_attempts: notification.delivery_attempts + 1,
        })
        .eq('id', notification.id);

      // Record delivery
      await this.supabase
        .from('notification_deliveries')
        .insert({
          scheduled_notification_id: notification.id,
          delivery_method: notification.notification_type,
          delivery_status: 'delivered',
          delivery_response: result,
        });

    } catch (error) {
      // Update notification as failed
      await this.supabase
        .from('scheduled_notifications')
        .update({
          status: 'failed',
          delivery_attempts: notification.delivery_attempts + 1,
          error_message: error.message,
        })
        .eq('id', notification.id);
    }
  }

  private async sendEmail(notification: ScheduledNotification): Promise<any> {
    // This would integrate with your email service
    const response = await fetch('/api/notifications/send-email', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(notification),
    });

    if (!response.ok) throw new Error('Email sending failed');
    return response.json();
  }

  private async sendSMS(notification: ScheduledNotification): Promise<any> {
    // This would integrate with your SMS service
    const response = await fetch('/api/notifications/send-sms', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(notification),
    });

    if (!response.ok) throw new Error('SMS sending failed');
    return response.json();
  }

  private async sendWebhook(notification: ScheduledNotification): Promise<any> {
    // Send webhook notification
    const response = await fetch(notification.recipient_email!, { // Using email field for webhook URL
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: notification.content,
    });

    if (!response.ok) throw new Error('Webhook sending failed');
    return { status: response.status };
  }

  private async getWorkflow(workflowId: string): Promise<Workflow | null> {
    const { data, error } = await this.supabase
      .from('workflows')
      .select('*')
      .eq('id', workflowId)
      .single();

    if (error) return null;
    return data;
  }

  private async getWorkflowExecution(executionId: string): Promise<WorkflowExecution | null> {
    const { data, error } = await this.supabase
      .from('workflow_executions')
      .select('*')
      .eq('id', executionId)
      .single();

    if (error) return null;
    return data;
  }
}
```

## Security Measures
- **Workflow Permissions**: Role-based access to workflow management
- **Execution Isolation**: Secure execution environment for workflows
- **Data Validation**: Comprehensive validation of workflow configurations
- **Rate Limiting**: Protection against workflow abuse
- **Audit Trail**: Complete logging of workflow executions
- **Secure Communications**: Encrypted notification delivery

## Integration Points
- **All Business Modules**: Event triggers from various business operations
- **Email Service**: Email notification delivery
- **User Management**: User-based workflow triggers and notifications
- **Customer Management**: Customer communication workflows
- **Invoice Management**: Invoice-based automation triggers

## Error Handling & Validation
- **Workflow Validation**: Comprehensive workflow configuration validation
- **Execution Error Handling**: Robust error handling with retry logic
- **Notification Delivery**: Retry mechanisms for failed notifications
- **Condition Evaluation**: Safe condition evaluation with error handling
- **Step Isolation**: Individual step failure handling

## Testing Strategy
- **Unit Tests**: Workflow service methods and condition evaluation
- **Integration Tests**: Workflow execution and notification delivery
- **E2E Tests**: Complete workflow automation scenarios
- **Performance Tests**: High-volume workflow execution
- **Reliability Tests**: Error handling and retry mechanisms

## Implementation Tasks
1. **Database Schema Setup**
   - Create workflow-related tables and indexes
   - Implement RLS policies for secure access
   - Set up notification scheduling system

2. **Workflow Engine Development**
   - Build WorkflowService with execution engine
   - Implement step execution handlers
   - Create condition evaluation system

3. **Notification System**
   - Build notification scheduling and delivery
   - Implement multi-channel notification support
   - Create delivery tracking and analytics

4. **Workflow Builder Interface**
   - Build visual workflow builder
   - Create step configuration interfaces
   - Implement workflow testing and debugging

5. **Automation Templates**
   - Create common workflow templates
   - Build template customization system
   - Implement template sharing and marketplace

6. **Monitoring and Analytics**
   - Build workflow execution monitoring
   - Create performance analytics dashboard
   - Implement workflow optimization suggestions

## Dependencies
- **All Business Modules**: Event sources for workflow triggers
- **Email Service**: Email notification delivery
- **SMS Service**: SMS notification delivery
- **Background Jobs**: Workflow execution and notification processing
- **Template System**: Email and notification templates

## Success Criteria
- ✅ Workflows can be created and configured easily
- ✅ Workflow execution is reliable and performant
- ✅ Notifications are delivered accurately and on time
- ✅ Error handling and retry logic works properly
- ✅ Workflow analytics provide valuable insights
- ✅ Common automation scenarios are well-supported
- ✅ Workflow builder interface is intuitive
- ✅ Performance is acceptable for high-volume automation