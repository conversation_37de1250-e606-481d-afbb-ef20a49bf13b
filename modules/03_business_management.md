# Multi-tenant Business Management

## Overview
This module implements the core multi-tenant architecture that allows multiple businesses to use the application while maintaining complete data isolation. It handles business creation, configuration, user management within businesses, subscription management, and business-specific settings.

## Core Functionalities
- Business registration and onboarding
- Multi-tenant data isolation with Row-Level Security
- Business profile management and branding
- Team member invitation and role management
- Subscription and billing management
- Business settings and preferences
- Business switching for users in multiple businesses
- Business analytics and usage tracking

## Technical Specifications

### Database Schema
```sql
-- Business settings table for flexible configuration
CREATE TABLE app.business_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    category TEXT NOT NULL,
    key TEXT NOT NULL,
    value JSONB NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(business_id, category, key)
);

-- Business subscription details
CREATE TABLE app.business_subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    stripe_customer_id TEXT UNIQUE,
    stripe_subscription_id TEXT UNIQUE,
    plan_name TEXT NOT NULL,
    plan_price NUMERIC(10, 2),
    billing_cycle TEXT NOT NULL DEFAULT 'monthly',
    status app.subscription_status NOT NULL DEFAULT 'trial',
    current_period_start TIMESTAMPTZ,
    current_period_end TIMESTAMPTZ,
    trial_start TIMESTAMPTZ,
    trial_end TIMESTAMPTZ,
    canceled_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Business usage tracking
CREATE TABLE app.business_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    metric_name TEXT NOT NULL,
    metric_value INTEGER NOT NULL DEFAULT 0,
    period_start TIMESTAMPTZ NOT NULL,
    period_end TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(business_id, metric_name, period_start)
);

-- Business activity log
CREATE TABLE app.business_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    user_id UUID REFERENCES app.users(id),
    activity_type TEXT NOT NULL,
    activity_data JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_business_settings_business_id ON app.business_settings(business_id);
CREATE INDEX idx_business_settings_category ON app.business_settings(business_id, category);
CREATE INDEX idx_business_subscriptions_business_id ON app.business_subscriptions(business_id);
CREATE INDEX idx_business_subscriptions_stripe ON app.business_subscriptions(stripe_customer_id, stripe_subscription_id);
CREATE INDEX idx_business_usage_business_metric ON app.business_usage(business_id, metric_name);
CREATE INDEX idx_business_activities_business_id ON app.business_activities(business_id);
CREATE INDEX idx_business_activities_created_at ON app.business_activities(created_at);

-- Enable RLS
ALTER TABLE app.business_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.business_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.business_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.business_activities ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Business members can view business settings"
ON app.business_settings FOR SELECT
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);

CREATE POLICY "Business admins can manage business settings"
ON app.business_settings FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() 
        AND role IN ('owner', 'admin')
        AND is_active = TRUE
    )
);

CREATE POLICY "Business owners can view subscription details"
ON app.business_subscriptions FOR SELECT
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() 
        AND role = 'owner'
        AND is_active = TRUE
    )
);
```

### Business Service
```typescript
// lib/business.ts
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

export interface Business {
  id: string;
  name: string;
  slug: string;
  email: string;
  phone?: string;
  website?: string;
  logo_url?: string;
  address?: BusinessAddress;
  tax_id?: string;
  currency: string;
  timezone: string;
  status: 'active' | 'suspended' | 'deleted';
  subscription_status: 'trial' | 'active' | 'past_due' | 'canceled' | 'unpaid';
  subscription_plan?: string;
  trial_ends_at?: string;
  created_at: string;
  updated_at: string;
}

export interface BusinessAddress {
  street: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
}

export interface BusinessUser {
  id: string;
  business_id: string;
  user_id: string;
  role: 'owner' | 'admin' | 'manager' | 'user' | 'viewer';
  invited_by?: string;
  invited_at?: string;
  joined_at?: string;
  is_active: boolean;
  user: {
    email: string;
    first_name: string;
    last_name: string;
    avatar_url?: string;
  };
}

export class BusinessService {
  private supabase = createClientComponentClient();

  async createBusiness(businessData: Partial<Business>): Promise<Business> {
    const { data, error } = await this.supabase
      .from('businesses')
      .insert({
        ...businessData,
        slug: this.generateSlug(businessData.name!),
      })
      .select()
      .single();

    if (error) throw error;

    // Add current user as owner
    await this.addUserToBusiness(data.id, 'owner');

    return data;
  }

  async updateBusiness(businessId: string, updates: Partial<Business>): Promise<Business> {
    const { data, error } = await this.supabase
      .from('businesses')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', businessId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getUserBusinesses(): Promise<Business[]> {
    const { data, error } = await this.supabase
      .from('business_users')
      .select(`
        business:businesses(*)
      `)
      .eq('is_active', true);

    if (error) throw error;
    return data.map(item => item.business).filter(Boolean);
  }

  async getBusinessUsers(businessId: string): Promise<BusinessUser[]> {
    const { data, error } = await this.supabase
      .from('business_users')
      .select(`
        *,
        user:users(email, first_name, last_name, avatar_url)
      `)
      .eq('business_id', businessId)
      .eq('is_active', true)
      .order('created_at');

    if (error) throw error;
    return data;
  }

  async inviteUserToBusiness(
    businessId: string,
    email: string,
    role: string
  ): Promise<void> {
    const invitationToken = crypto.randomUUID();
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days expiry

    const { error } = await this.supabase
      .from('business_invitations')
      .insert({
        business_id: businessId,
        email,
        role,
        invitation_token: invitationToken,
        expires_at: expiresAt.toISOString(),
      });

    if (error) throw error;

    // Send invitation email
    await this.sendInvitationEmail(email, invitationToken, businessId);
  }

  async acceptInvitation(token: string): Promise<void> {
    const { data: invitation, error: fetchError } = await this.supabase
      .from('business_invitations')
      .select('*')
      .eq('invitation_token', token)
      .gt('expires_at', new Date().toISOString())
      .is('accepted_at', null)
      .single();

    if (fetchError || !invitation) {
      throw new Error('Invalid or expired invitation');
    }

    // Add user to business
    await this.addUserToBusiness(invitation.business_id, invitation.role);

    // Mark invitation as accepted
    await this.supabase
      .from('business_invitations')
      .update({
        accepted_at: new Date().toISOString(),
        accepted_by: (await this.supabase.auth.getUser()).data.user?.id,
      })
      .eq('id', invitation.id);
  }

  async removeUserFromBusiness(businessId: string, userId: string): Promise<void> {
    const { error } = await this.supabase
      .from('business_users')
      .update({ is_active: false })
      .eq('business_id', businessId)
      .eq('user_id', userId);

    if (error) throw error;
  }

  async updateUserRole(
    businessId: string,
    userId: string,
    newRole: string
  ): Promise<void> {
    const { error } = await this.supabase
      .from('business_users')
      .update({ role: newRole })
      .eq('business_id', businessId)
      .eq('user_id', userId);

    if (error) throw error;
  }

  async getBusinessSettings(businessId: string, category?: string): Promise<Record<string, any>> {
    let query = this.supabase
      .from('business_settings')
      .select('key, value')
      .eq('business_id', businessId);

    if (category) {
      query = query.eq('category', category);
    }

    const { data, error } = await query;
    if (error) throw error;

    return data.reduce((acc, setting) => {
      acc[setting.key] = setting.value;
      return acc;
    }, {});
  }

  async updateBusinessSetting(
    businessId: string,
    category: string,
    key: string,
    value: any
  ): Promise<void> {
    const { error } = await this.supabase
      .from('business_settings')
      .upsert({
        business_id: businessId,
        category,
        key,
        value,
        updated_at: new Date().toISOString(),
      });

    if (error) throw error;
  }

  private async addUserToBusiness(businessId: string, role: string): Promise<void> {
    const user = await this.supabase.auth.getUser();
    if (!user.data.user) throw new Error('User not authenticated');

    const { error } = await this.supabase
      .from('business_users')
      .insert({
        business_id: businessId,
        user_id: user.data.user.id,
        role,
        joined_at: new Date().toISOString(),
      });

    if (error) throw error;
  }

  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  }

  private async sendInvitationEmail(
    email: string,
    token: string,
    businessId: string
  ): Promise<void> {
    // Implementation depends on email service (Resend, SendGrid, etc.)
    // This would typically call an Edge Function or API route
    await fetch('/api/send-invitation', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, token, businessId }),
    });
  }
}
```

### React Hooks for Business Management
```typescript
// hooks/useBusiness.ts
import { useEffect, useState } from 'react';
import { BusinessService, Business } from '@/lib/business';

export function useBusiness() {
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [currentBusiness, setCurrentBusiness] = useState<Business | null>(null);
  const [loading, setLoading] = useState(true);
  const businessService = new BusinessService();

  useEffect(() => {
    loadBusinesses();
  }, []);

  const loadBusinesses = async () => {
    try {
      const userBusinesses = await businessService.getUserBusinesses();
      setBusinesses(userBusinesses);
      
      // Set current business from localStorage or first business
      const savedBusinessId = localStorage.getItem('currentBusinessId');
      const current = savedBusinessId 
        ? userBusinesses.find(b => b.id === savedBusinessId)
        : userBusinesses[0];
      
      if (current) {
        setCurrentBusiness(current);
        localStorage.setItem('currentBusinessId', current.id);
      }
    } catch (error) {
      console.error('Failed to load businesses:', error);
    } finally {
      setLoading(false);
    }
  };

  const switchBusiness = (business: Business) => {
    setCurrentBusiness(business);
    localStorage.setItem('currentBusinessId', business.id);
  };

  const createBusiness = async (businessData: Partial<Business>) => {
    const newBusiness = await businessService.createBusiness(businessData);
    setBusinesses(prev => [...prev, newBusiness]);
    switchBusiness(newBusiness);
    return newBusiness;
  };

  return {
    businesses,
    currentBusiness,
    loading,
    switchBusiness,
    createBusiness,
    refreshBusinesses: loadBusinesses,
  };
}
```

## Security Measures
- **Row-Level Security**: Complete data isolation between businesses
- **Role-Based Access**: Granular permissions for business operations
- **Invitation Security**: Secure token-based invitation system
- **Audit Logging**: Complete activity tracking for business operations
- **Data Validation**: Comprehensive input validation and sanitization
- **Subscription Enforcement**: Usage limits based on subscription plans

## Integration Points
- **Authentication & Authorization**: User roles and permissions
- **All Data Modules**: Business context for all business data
- **Payment Processing**: Subscription and billing management
- **Audit Logging**: Business activity tracking
- **Settings Configuration**: Business-specific settings

## Error Handling & Validation
- **Business Validation**: Name uniqueness, slug generation
- **Invitation Validation**: Email format, expiry checking
- **Role Validation**: Valid role assignments and permissions
- **Subscription Validation**: Plan limits and usage tracking
- **Data Consistency**: Transaction-based operations

## Testing Strategy
- **Unit Tests**: Business service methods and utilities
- **Integration Tests**: Multi-tenant data isolation
- **E2E Tests**: Complete business onboarding flow
- **Security Tests**: RLS policy effectiveness
- **Performance Tests**: Multi-tenant query performance

## Implementation Tasks
1. **Database Schema Setup**
   - Create business-related tables and indexes
   - Implement RLS policies for data isolation
   - Set up business settings and subscription tables

2. **Business Service Development**
   - Implement BusinessService class
   - Build invitation and user management
   - Create business settings management

3. **Frontend Integration**
   - Build business selection and switching UI
   - Create business onboarding flow
   - Implement team management interface

4. **Subscription Management**
   - Integrate with Stripe for billing
   - Implement usage tracking and limits
   - Build subscription management UI

5. **Multi-tenant Testing**
   - Test data isolation between businesses
   - Verify RLS policy effectiveness
   - Test business switching functionality

## Dependencies
- **Core Infrastructure**: Database foundation and RLS
- **Authentication & Authorization**: User management and roles
- **Payment Processing**: Subscription and billing integration
- **Email Service**: Invitation email delivery

## Success Criteria
- ✅ Complete data isolation between businesses
- ✅ Users can create and manage businesses
- ✅ Team invitation system works end-to-end
- ✅ Business switching works seamlessly
- ✅ Subscription management is functional
- ✅ All RLS policies prevent unauthorized access
- ✅ Business settings can be configured and persisted
