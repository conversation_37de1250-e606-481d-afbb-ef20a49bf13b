# Multi-tenant Business Management

## Overview
This module implements the core multi-tenant architecture that allows multiple businesses to use the application while maintaining complete data isolation. It handles business creation, configuration, user management within businesses, subscription management, and business-specific settings.

## Core Functionalities
- Business registration and onboarding
- Multi-tenant data isolation with Row-Level Security
- Business profile management and branding
- Team member invitation and role management
- Subscription and billing management
- Business settings and preferences
- Business switching for users in multiple businesses
- Business analytics and usage tracking

## Technical Specifications

### Database Schema
```sql
-- Business settings table for flexible configuration
CREATE TABLE app.business_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    category TEXT NOT NULL,
    key TEXT NOT NULL,
    value JSONB NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(business_id, category, key)
);

-- Business subscription details
CREATE TABLE app.business_subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    stripe_customer_id TEXT UNIQUE,
    stripe_subscription_id TEXT UNIQUE,
    plan_name TEXT NOT NULL,
    plan_price NUMERIC(10, 2),
    billing_cycle TEXT NOT NULL DEFAULT 'monthly',
    status app.subscription_status NOT NULL DEFAULT 'trial',
    current_period_start TIMESTAMPTZ,
    current_period_end TIMESTAMPTZ,
    trial_start TIMESTAMPTZ,
    trial_end TIMESTAMPTZ,
    canceled_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Business usage tracking
CREATE TABLE app.business_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    metric_name TEXT NOT NULL,
    metric_value INTEGER NOT NULL DEFAULT 0,
    period_start TIMESTAMPTZ NOT NULL,
    period_end TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(business_id, metric_name, period_start)
);

-- Business activity log
CREATE TABLE app.business_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    user_id UUID REFERENCES app.users(id),
    activity_type TEXT NOT NULL,
    activity_data JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_business_settings_business_id ON app.business_settings(business_id);
CREATE INDEX idx_business_settings_category ON app.business_settings(business_id, category);
CREATE INDEX idx_business_subscriptions_business_id ON app.business_subscriptions(business_id);
CREATE INDEX idx_business_subscriptions_stripe ON app.business_subscriptions(stripe_customer_id, stripe_subscription_id);
CREATE INDEX idx_business_usage_business_metric ON app.business_usage(business_id, metric_name);
CREATE INDEX idx_business_activities_business_id ON app.business_activities(business_id);
CREATE INDEX idx_business_activities_created_at ON app.business_activities(created_at);

-- Enable RLS
ALTER TABLE app.business_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.business_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.business_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.business_activities ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Business members can view business settings"
ON app.business_settings FOR SELECT
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);

CREATE POLICY "Business admins can manage business settings"
ON app.business_settings FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() 
        AND role IN ('owner', 'admin')
        AND is_active = TRUE
    )
);

CREATE POLICY "Business owners can view subscription details"
ON app.business_subscriptions FOR SELECT
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() 
        AND role = 'owner'
        AND is_active = TRUE
    )
);
```

### Business Service Architecture

**Core Service: BusinessService**
- **Primary Responsibilities**: Multi-tenant business management, team administration, subscription handling
- **Key Methods**:
  - `createBusiness(businessData)` - Business registration and initial setup
  - `updateBusiness(businessId, updates)` - Business profile and settings management
  - `getUserBusinesses()` - Retrieve businesses accessible to current user
  - `inviteUserToBusiness(businessId, email, role)` - Team member invitation system
  - `acceptInvitation(token)` - Invitation acceptance and user onboarding
  - `updateUserRole(businessId, userId, newRole)` - Role management and permissions
  - `getBusinessSettings(businessId, category)` - Business configuration retrieval
  - `updateBusinessSetting(businessId, category, key, value)` - Settings management

**Implementation Approach**:
- Implement secure invitation system with token-based authentication
- Build business switching mechanism for multi-business users
- Create comprehensive settings management with categorization
- Establish subscription and billing integration with Stripe
- Implement business slug generation for friendly URLs

### Multi-Tenant Data Architecture

**Business Context Management**:
- **Business Switching**: Seamless switching between multiple businesses for users
- **Data Isolation**: Complete separation of business data using RLS policies
- **Role Inheritance**: User roles are business-specific, not global
- **Settings Hierarchy**: Business-level settings override system defaults

**Team Management System**:
- **Invitation Workflow**: Secure email-based invitation system with expiration
- **Role Assignment**: Granular role-based permissions within each business
- **User Onboarding**: Guided setup process for new team members
- **Access Control**: Dynamic permission checking based on business context

### Frontend Integration Strategy

**Business Management Hooks**:
- **useBusiness()**: Business context management and switching
- **useBusinessUsers()**: Team member management and role administration
- **useBusinessSettings()**: Business configuration and preferences

**Business Context Provider**:
- Global business context for the entire application
- Automatic business switching and state management
- Permission-aware component rendering

## Security Measures
- **Row-Level Security**: Complete data isolation between businesses
- **Role-Based Access**: Granular permissions for business operations
- **Invitation Security**: Secure token-based invitation system
- **Audit Logging**: Complete activity tracking for business operations
- **Data Validation**: Comprehensive input validation and sanitization
- **Subscription Enforcement**: Usage limits based on subscription plans

## Integration Points
- **Authentication & Authorization**: User roles and permissions
- **All Data Modules**: Business context for all business data
- **Payment Processing**: Subscription and billing management
- **Audit Logging**: Business activity tracking
- **Settings Configuration**: Business-specific settings

## Error Handling & Validation
- **Business Validation**: Name uniqueness, slug generation
- **Invitation Validation**: Email format, expiry checking
- **Role Validation**: Valid role assignments and permissions
- **Subscription Validation**: Plan limits and usage tracking
- **Data Consistency**: Transaction-based operations

## Testing Strategy
- **Unit Tests**: Business service methods and utilities
- **Integration Tests**: Multi-tenant data isolation
- **E2E Tests**: Complete business onboarding flow
- **Security Tests**: RLS policy effectiveness
- **Performance Tests**: Multi-tenant query performance

## Implementation Tasks
1. **Database Schema Setup**
   - Create business-related tables and indexes
   - Implement RLS policies for data isolation
   - Set up business settings and subscription tables

2. **Business Service Development**
   - Implement BusinessService class
   - Build invitation and user management
   - Create business settings management

3. **Frontend Integration**
   - Build business selection and switching UI
   - Create business onboarding flow
   - Implement team management interface

4. **Subscription Management**
   - Integrate with Stripe for billing
   - Implement usage tracking and limits
   - Build subscription management UI

5. **Multi-tenant Testing**
   - Test data isolation between businesses
   - Verify RLS policy effectiveness
   - Test business switching functionality

## Dependencies
- **Core Infrastructure**: Database foundation and RLS
- **Authentication & Authorization**: User management and roles
- **Payment Processing**: Subscription and billing integration
- **Email Service**: Invitation email delivery

## Success Criteria
- ✅ Complete data isolation between businesses
- ✅ Users can create and manage businesses
- ✅ Team invitation system works end-to-end
- ✅ Business switching works seamlessly
- ✅ Subscription management is functional
- ✅ All RLS policies prevent unauthorized access
- ✅ Business settings can be configured and persisted
