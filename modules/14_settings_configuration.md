# Settings & Configuration

## Overview
This module provides comprehensive settings and configuration management for businesses and users, including business preferences, system configurations, notification settings, and customization options. It ensures that all settings are properly scoped, validated, and easily manageable through intuitive interfaces.

## Core Functionalities
- Business-wide settings and preferences
- User-specific settings and preferences
- System configuration management
- Notification and email preferences
- Invoice and document templates
- Tax settings and compliance configuration
- Currency and localization settings
- Integration and API configurations
- Backup and export settings
- Theme and branding customization

## Technical Specifications

### Database Schema
```sql
-- Settings categories enum
CREATE TYPE app.setting_category AS ENUM (
    'general', 'billing', 'notifications', 'integrations', 'security', 
    'appearance', 'localization', 'tax', 'templates', 'api'
);

-- Setting data types
CREATE TYPE app.setting_type AS ENUM (
    'string', 'number', 'boolean', 'json', 'array', 'date', 'email', 'url'
);

-- Business settings
CREATE TABLE app.business_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    category app.setting_category NOT NULL,
    key TEXT NOT NULL,
    value JSONB NOT NULL,
    data_type app.setting_type NOT NULL DEFAULT 'string',
    
    -- Metadata
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE, -- Visible to all business users
    is_required BOOLEAN DEFAULT FALSE,
    default_value JSONB,
    validation_rules JSONB, -- JSON schema for validation
    
    -- Versioning
    version INTEGER DEFAULT 1,
    previous_value JSONB,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    updated_by UUID REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, category, key)
);

-- User settings (personal preferences)
CREATE TABLE app.user_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES app.users(id) ON DELETE CASCADE,
    business_id UUID REFERENCES app.businesses(id) ON DELETE CASCADE, -- NULL for global user settings
    
    category app.setting_category NOT NULL,
    key TEXT NOT NULL,
    value JSONB NOT NULL,
    data_type app.setting_type NOT NULL DEFAULT 'string',
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(user_id, business_id, category, key)
);

-- Setting definitions (schema for available settings)
CREATE TABLE app.setting_definitions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    category app.setting_category NOT NULL,
    key TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    data_type app.setting_type NOT NULL,
    
    -- Validation and constraints
    is_required BOOLEAN DEFAULT FALSE,
    default_value JSONB,
    validation_rules JSONB,
    possible_values JSONB, -- For enum-like settings
    
    -- UI configuration
    ui_component TEXT, -- 'input', 'select', 'checkbox', 'textarea', etc.
    ui_props JSONB, -- Component-specific properties
    display_order INTEGER DEFAULT 0,
    
    -- Scope
    scope TEXT NOT NULL DEFAULT 'business' CHECK (scope IN ('business', 'user', 'system')),
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(category, key)
);

-- Notification preferences
CREATE TABLE app.notification_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES app.users(id) ON DELETE CASCADE,
    business_id UUID REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    notification_type TEXT NOT NULL,
    
    -- Delivery channels
    email_enabled BOOLEAN DEFAULT TRUE,
    in_app_enabled BOOLEAN DEFAULT TRUE,
    sms_enabled BOOLEAN DEFAULT FALSE,
    push_enabled BOOLEAN DEFAULT TRUE,
    
    -- Frequency settings
    frequency TEXT DEFAULT 'immediate' CHECK (frequency IN ('immediate', 'daily', 'weekly', 'never')),
    quiet_hours_start TIME,
    quiet_hours_end TIME,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(user_id, business_id, notification_type)
);

-- Email templates
CREATE TABLE app.email_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    template_type TEXT NOT NULL, -- 'invoice', 'reminder', 'welcome', etc.
    name TEXT NOT NULL,
    subject TEXT NOT NULL,
    body_html TEXT NOT NULL,
    body_text TEXT,
    
    -- Template variables
    variables JSONB, -- Available variables for this template
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    updated_by UUID REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, template_type, name)
);

-- Tax configurations
CREATE TABLE app.tax_configurations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    tax_name TEXT NOT NULL,
    tax_rate NUMERIC(5, 4) NOT NULL CHECK (tax_rate >= 0 AND tax_rate <= 1),
    tax_type TEXT NOT NULL CHECK (tax_type IN ('percentage', 'fixed')),
    
    -- Applicability
    applies_to TEXT[] DEFAULT ARRAY['products', 'services'], -- What this tax applies to
    country_code TEXT,
    state_province TEXT,
    
    -- Configuration
    is_compound BOOLEAN DEFAULT FALSE, -- Tax on tax
    is_inclusive BOOLEAN DEFAULT FALSE, -- Tax included in price
    is_default BOOLEAN DEFAULT FALSE,
    
    -- Validity
    effective_from DATE,
    effective_until DATE,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- System configurations (global settings)
CREATE TABLE app.system_configurations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    category TEXT NOT NULL,
    key TEXT NOT NULL,
    value JSONB NOT NULL,
    data_type app.setting_type NOT NULL DEFAULT 'string',
    
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE, -- Visible to all users
    
    created_by UUID REFERENCES app.users(id),
    updated_by UUID REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(category, key)
);

-- Indexes for performance
CREATE INDEX idx_business_settings_business_category ON app.business_settings(business_id, category);
CREATE INDEX idx_business_settings_key ON app.business_settings(business_id, key);
CREATE INDEX idx_user_settings_user_business ON app.user_settings(user_id, business_id);
CREATE INDEX idx_user_settings_category ON app.user_settings(user_id, category);
CREATE INDEX idx_setting_definitions_category ON app.setting_definitions(category);
CREATE INDEX idx_notification_preferences_user ON app.notification_preferences(user_id, business_id);
CREATE INDEX idx_email_templates_business_type ON app.email_templates(business_id, template_type);
CREATE INDEX idx_tax_configurations_business_id ON app.tax_configurations(business_id);
CREATE INDEX idx_system_configurations_category ON app.system_configurations(category);

-- Enable RLS
ALTER TABLE app.business_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.setting_definitions ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.notification_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.email_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.tax_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.system_configurations ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Business members can view business settings"
ON app.business_settings FOR SELECT
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);

CREATE POLICY "Business admins can manage business settings"
ON app.business_settings FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() 
        AND role IN ('owner', 'admin')
        AND is_active = TRUE
    )
);

CREATE POLICY "Users can manage their own settings"
ON app.user_settings FOR ALL
USING (user_id = auth.uid());

CREATE POLICY "Users can view their notification preferences"
ON app.notification_preferences FOR ALL
USING (user_id = auth.uid());

CREATE POLICY "Business members can view email templates"
ON app.email_templates FOR SELECT
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);

CREATE POLICY "Business admins can manage email templates"
ON app.email_templates FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() 
        AND role IN ('owner', 'admin')
        AND is_active = TRUE
    )
);

CREATE POLICY "Everyone can view public setting definitions"
ON app.setting_definitions FOR SELECT
USING (true);

CREATE POLICY "Everyone can view public system configurations"
ON app.system_configurations FOR SELECT
USING (is_public = true);
```

### Settings Service Architecture

**Core Service: SettingsService**
- **Primary Responsibilities**: Configuration management, preference handling, system settings, customization options
- **Key Methods**:
  - `getSettings(businessId, category)` - Settings retrieval with category filtering
  - `updateSettings(businessId, settingsData)` - Settings modification with validation
  - `resetSettings(businessId, category)` - Settings reset to defaults
  - `getDefaultSettings(category)` - Default configuration retrieval
  - `validateSettings(settingsData)` - Settings validation and constraint checking
  - `exportSettings(businessId, format)` - Settings export for backup and migration
  - `importSettings(businessId, settingsData)` - Settings import with conflict resolution
  - `getSettingsHistory(businessId, settingKey)` - Settings change history tracking

**Implementation Approach**:
- Build hierarchical settings system with category-based organization
- Implement settings validation with business rule enforcement
- Create settings migration system for version upgrades
- Establish settings backup and restore capabilities
- Build user preference management with role-based access

### Settings Management Strategy

**Configuration Hierarchy**:
- **System Settings**: Global application configuration and defaults
- **Business Settings**: Business-specific configuration and preferences
- **User Settings**: Individual user preferences and customizations
- **Module Settings**: Feature-specific configuration options
- **Integration Settings**: Third-party service configuration and API keys

**Settings Categories**:
- **General**: Basic business information, timezone, currency, language
- **Billing**: Invoice templates, payment terms, tax settings
- **Notifications**: Email preferences, alert settings, communication channels
- **Security**: Authentication settings, access controls, session management
- **Integrations**: API configurations, webhook settings, external service connections
- **Appearance**: UI themes, branding, layout preferences

**Configuration Management**:
- **Validation Engine**: Comprehensive validation with business rule enforcement
- **Version Control**: Settings versioning with rollback capabilities
- **Migration System**: Automated settings migration for application updates
- **Backup & Restore**: Settings backup with scheduled exports and import capabilities
- **Audit Trail**: Complete history of settings changes with user attribution




## Security Measures
- **Setting Validation**: Comprehensive validation rules and type checking
- **Access Control**: Role-based permissions for different setting categories
- **Audit Trail**: Complete tracking of setting changes with versioning
- **Data Encryption**: Sensitive settings encrypted at rest
- **Input Sanitization**: Proper sanitization of all setting values
- **Schema Validation**: JSON schema validation for complex settings

## Integration Points
- **All Modules**: Settings provide configuration for all business operations
- **User Management**: User-specific preferences and settings
- **Business Management**: Business-wide configuration and preferences
- **Email Service**: Email template and notification settings
- **Tax Management**: Tax configuration and compliance settings

## Error Handling & Validation
- **Setting Validation**: Comprehensive validation against defined schemas
- **Type Safety**: Strong typing and data type validation
- **Default Values**: Fallback to default values for missing settings
- **Import Validation**: Robust validation during settings import
- **Error Recovery**: Graceful handling of invalid or corrupted settings

## Testing Strategy
- **Unit Tests**: Settings service methods and validation logic
- **Integration Tests**: Settings persistence and retrieval
- **Validation Tests**: Setting validation rules and constraints
- **Import/Export Tests**: Settings import and export functionality
- **Security Tests**: Access control and permission validation

## Implementation Tasks
1. **Database Schema Setup**
   - Create settings-related tables and indexes
   - Implement RLS policies for secure access
   - Set up setting definitions and validation rules

2. **Settings Service Development**
   - Build SettingsService with all CRUD operations
   - Implement validation and type checking
   - Create import/export functionality

3. **Frontend Components**
   - Build settings management interface
   - Create dynamic form generation from definitions
   - Implement settings categories and organization

4. **Notification System**
   - Build notification preference management
   - Create email template editor
   - Implement notification delivery settings

5. **Tax Configuration**
   - Build tax configuration interface
   - Implement tax calculation settings
   - Create compliance configuration options

6. **Advanced Features**
   - Build settings versioning and rollback
   - Implement settings templates and presets
   - Create bulk settings operations

## Dependencies
- **Business Management**: Business context for settings
- **User Management**: User-specific settings and preferences
- **Email Service**: Email template and notification configuration
- **Tax Management**: Tax configuration and compliance settings
- **Audit Logging**: Settings change tracking and versioning

## Success Criteria
- ✅ All settings can be configured and managed easily
- ✅ Setting validation prevents invalid configurations
- ✅ User preferences are saved and applied correctly
- ✅ Email templates can be customized and used
- ✅ Tax configurations work accurately
- ✅ Settings import/export functionality works reliably
- ✅ All settings are properly scoped and secured
- ✅ Settings interface is intuitive and user-friendly
