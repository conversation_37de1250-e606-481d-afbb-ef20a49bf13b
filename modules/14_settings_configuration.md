# Settings & Configuration

## Overview
This module provides comprehensive settings and configuration management for businesses and users, including business preferences, system configurations, notification settings, and customization options. It ensures that all settings are properly scoped, validated, and easily manageable through intuitive interfaces.

## Core Functionalities
- Business-wide settings and preferences
- User-specific settings and preferences
- System configuration management
- Notification and email preferences
- Invoice and document templates
- Tax settings and compliance configuration
- Currency and localization settings
- Integration and API configurations
- Backup and export settings
- Theme and branding customization

## Technical Specifications

### Database Schema
```sql
-- Settings categories enum
CREATE TYPE app.setting_category AS ENUM (
    'general', 'billing', 'notifications', 'integrations', 'security', 
    'appearance', 'localization', 'tax', 'templates', 'api'
);

-- Setting data types
CREATE TYPE app.setting_type AS ENUM (
    'string', 'number', 'boolean', 'json', 'array', 'date', 'email', 'url'
);

-- Business settings
CREATE TABLE app.business_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    category app.setting_category NOT NULL,
    key TEXT NOT NULL,
    value JSONB NOT NULL,
    data_type app.setting_type NOT NULL DEFAULT 'string',
    
    -- Metadata
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE, -- Visible to all business users
    is_required BOOLEAN DEFAULT FALSE,
    default_value JSONB,
    validation_rules JSONB, -- JSON schema for validation
    
    -- Versioning
    version INTEGER DEFAULT 1,
    previous_value JSONB,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    updated_by UUID REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, category, key)
);

-- User settings (personal preferences)
CREATE TABLE app.user_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES app.users(id) ON DELETE CASCADE,
    business_id UUID REFERENCES app.businesses(id) ON DELETE CASCADE, -- NULL for global user settings
    
    category app.setting_category NOT NULL,
    key TEXT NOT NULL,
    value JSONB NOT NULL,
    data_type app.setting_type NOT NULL DEFAULT 'string',
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(user_id, business_id, category, key)
);

-- Setting definitions (schema for available settings)
CREATE TABLE app.setting_definitions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    category app.setting_category NOT NULL,
    key TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    data_type app.setting_type NOT NULL,
    
    -- Validation and constraints
    is_required BOOLEAN DEFAULT FALSE,
    default_value JSONB,
    validation_rules JSONB,
    possible_values JSONB, -- For enum-like settings
    
    -- UI configuration
    ui_component TEXT, -- 'input', 'select', 'checkbox', 'textarea', etc.
    ui_props JSONB, -- Component-specific properties
    display_order INTEGER DEFAULT 0,
    
    -- Scope
    scope TEXT NOT NULL DEFAULT 'business' CHECK (scope IN ('business', 'user', 'system')),
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(category, key)
);

-- Notification preferences
CREATE TABLE app.notification_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES app.users(id) ON DELETE CASCADE,
    business_id UUID REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    notification_type TEXT NOT NULL,
    
    -- Delivery channels
    email_enabled BOOLEAN DEFAULT TRUE,
    in_app_enabled BOOLEAN DEFAULT TRUE,
    sms_enabled BOOLEAN DEFAULT FALSE,
    push_enabled BOOLEAN DEFAULT TRUE,
    
    -- Frequency settings
    frequency TEXT DEFAULT 'immediate' CHECK (frequency IN ('immediate', 'daily', 'weekly', 'never')),
    quiet_hours_start TIME,
    quiet_hours_end TIME,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(user_id, business_id, notification_type)
);

-- Email templates
CREATE TABLE app.email_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    template_type TEXT NOT NULL, -- 'invoice', 'reminder', 'welcome', etc.
    name TEXT NOT NULL,
    subject TEXT NOT NULL,
    body_html TEXT NOT NULL,
    body_text TEXT,
    
    -- Template variables
    variables JSONB, -- Available variables for this template
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    updated_by UUID REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, template_type, name)
);

-- Tax configurations
CREATE TABLE app.tax_configurations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    tax_name TEXT NOT NULL,
    tax_rate NUMERIC(5, 4) NOT NULL CHECK (tax_rate >= 0 AND tax_rate <= 1),
    tax_type TEXT NOT NULL CHECK (tax_type IN ('percentage', 'fixed')),
    
    -- Applicability
    applies_to TEXT[] DEFAULT ARRAY['products', 'services'], -- What this tax applies to
    country_code TEXT,
    state_province TEXT,
    
    -- Configuration
    is_compound BOOLEAN DEFAULT FALSE, -- Tax on tax
    is_inclusive BOOLEAN DEFAULT FALSE, -- Tax included in price
    is_default BOOLEAN DEFAULT FALSE,
    
    -- Validity
    effective_from DATE,
    effective_until DATE,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- System configurations (global settings)
CREATE TABLE app.system_configurations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    category TEXT NOT NULL,
    key TEXT NOT NULL,
    value JSONB NOT NULL,
    data_type app.setting_type NOT NULL DEFAULT 'string',
    
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE, -- Visible to all users
    
    created_by UUID REFERENCES app.users(id),
    updated_by UUID REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(category, key)
);

-- Indexes for performance
CREATE INDEX idx_business_settings_business_category ON app.business_settings(business_id, category);
CREATE INDEX idx_business_settings_key ON app.business_settings(business_id, key);
CREATE INDEX idx_user_settings_user_business ON app.user_settings(user_id, business_id);
CREATE INDEX idx_user_settings_category ON app.user_settings(user_id, category);
CREATE INDEX idx_setting_definitions_category ON app.setting_definitions(category);
CREATE INDEX idx_notification_preferences_user ON app.notification_preferences(user_id, business_id);
CREATE INDEX idx_email_templates_business_type ON app.email_templates(business_id, template_type);
CREATE INDEX idx_tax_configurations_business_id ON app.tax_configurations(business_id);
CREATE INDEX idx_system_configurations_category ON app.system_configurations(category);

-- Enable RLS
ALTER TABLE app.business_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.setting_definitions ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.notification_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.email_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.tax_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.system_configurations ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Business members can view business settings"
ON app.business_settings FOR SELECT
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);

CREATE POLICY "Business admins can manage business settings"
ON app.business_settings FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() 
        AND role IN ('owner', 'admin')
        AND is_active = TRUE
    )
);

CREATE POLICY "Users can manage their own settings"
ON app.user_settings FOR ALL
USING (user_id = auth.uid());

CREATE POLICY "Users can view their notification preferences"
ON app.notification_preferences FOR ALL
USING (user_id = auth.uid());

CREATE POLICY "Business members can view email templates"
ON app.email_templates FOR SELECT
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);

CREATE POLICY "Business admins can manage email templates"
ON app.email_templates FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() 
        AND role IN ('owner', 'admin')
        AND is_active = TRUE
    )
);

CREATE POLICY "Everyone can view public setting definitions"
ON app.setting_definitions FOR SELECT
USING (true);

CREATE POLICY "Everyone can view public system configurations"
ON app.system_configurations FOR SELECT
USING (is_public = true);
```

### Settings Service
```typescript
// lib/settings.ts
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

export interface BusinessSetting {
  id: string;
  business_id: string;
  category: string;
  key: string;
  value: any;
  data_type: string;
  description?: string;
  is_public: boolean;
  is_required: boolean;
  default_value?: any;
  validation_rules?: any;
  version: number;
  previous_value?: any;
  created_at: string;
  updated_at: string;
}

export interface UserSetting {
  id: string;
  user_id: string;
  business_id?: string;
  category: string;
  key: string;
  value: any;
  data_type: string;
  created_at: string;
  updated_at: string;
}

export interface SettingDefinition {
  id: string;
  category: string;
  key: string;
  name: string;
  description?: string;
  data_type: string;
  is_required: boolean;
  default_value?: any;
  validation_rules?: any;
  possible_values?: any;
  ui_component: string;
  ui_props?: any;
  display_order: number;
  scope: 'business' | 'user' | 'system';
  is_active: boolean;
}

export interface NotificationPreference {
  id: string;
  user_id: string;
  business_id?: string;
  notification_type: string;
  email_enabled: boolean;
  in_app_enabled: boolean;
  sms_enabled: boolean;
  push_enabled: boolean;
  frequency: 'immediate' | 'daily' | 'weekly' | 'never';
  quiet_hours_start?: string;
  quiet_hours_end?: string;
}

export interface EmailTemplate {
  id: string;
  business_id: string;
  template_type: string;
  name: string;
  subject: string;
  body_html: string;
  body_text?: string;
  variables?: any;
  is_active: boolean;
  is_default: boolean;
}

export interface TaxConfiguration {
  id: string;
  business_id: string;
  tax_name: string;
  tax_rate: number;
  tax_type: 'percentage' | 'fixed';
  applies_to: string[];
  country_code?: string;
  state_province?: string;
  is_compound: boolean;
  is_inclusive: boolean;
  is_default: boolean;
  effective_from?: string;
  effective_until?: string;
}

export class SettingsService {
  private supabase = createClientComponentClient();

  // Business Settings
  async getBusinessSettings(businessId: string, category?: string): Promise<Record<string, any>> {
    let query = this.supabase
      .from('business_settings')
      .select('category, key, value, data_type')
      .eq('business_id', businessId);

    if (category) {
      query = query.eq('category', category);
    }

    const { data, error } = await query;
    if (error) throw error;

    return this.flattenSettings(data || []);
  }

  async updateBusinessSetting(
    businessId: string,
    category: string,
    key: string,
    value: any,
    dataType: string = 'string'
  ): Promise<void> {
    // Get current value for versioning
    const { data: current } = await this.supabase
      .from('business_settings')
      .select('value, version')
      .eq('business_id', businessId)
      .eq('category', category)
      .eq('key', key)
      .single();

    const user = await this.supabase.auth.getUser();

    const { error } = await this.supabase
      .from('business_settings')
      .upsert({
        business_id: businessId,
        category,
        key,
        value,
        data_type: dataType,
        version: (current?.version || 0) + 1,
        previous_value: current?.value,
        updated_by: user.data.user?.id,
        updated_at: new Date().toISOString(),
        ...(current ? {} : { created_by: user.data.user?.id }),
      });

    if (error) throw error;
  }

  async bulkUpdateBusinessSettings(
    businessId: string,
    settings: Array<{ category: string; key: string; value: any; dataType?: string }>
  ): Promise<void> {
    const user = await this.supabase.auth.getUser();
    const now = new Date().toISOString();

    const settingsToUpsert = settings.map(setting => ({
      business_id: businessId,
      category: setting.category,
      key: setting.key,
      value: setting.value,
      data_type: setting.dataType || 'string',
      updated_by: user.data.user?.id,
      updated_at: now,
      created_by: user.data.user?.id,
    }));

    const { error } = await this.supabase
      .from('business_settings')
      .upsert(settingsToUpsert);

    if (error) throw error;
  }

  // User Settings
  async getUserSettings(userId?: string, businessId?: string, category?: string): Promise<Record<string, any>> {
    const user = await this.supabase.auth.getUser();
    const targetUserId = userId || user.data.user?.id;

    let query = this.supabase
      .from('user_settings')
      .select('category, key, value, data_type')
      .eq('user_id', targetUserId);

    if (businessId) {
      query = query.eq('business_id', businessId);
    }

    if (category) {
      query = query.eq('category', category);
    }

    const { data, error } = await query;
    if (error) throw error;

    return this.flattenSettings(data || []);
  }

  async updateUserSetting(
    category: string,
    key: string,
    value: any,
    businessId?: string,
    dataType: string = 'string'
  ): Promise<void> {
    const user = await this.supabase.auth.getUser();
    if (!user.data.user) throw new Error('User not authenticated');

    const { error } = await this.supabase
      .from('user_settings')
      .upsert({
        user_id: user.data.user.id,
        business_id: businessId,
        category,
        key,
        value,
        data_type: dataType,
        updated_at: new Date().toISOString(),
      });

    if (error) throw error;
  }

  // Setting Definitions
  async getSettingDefinitions(scope?: 'business' | 'user' | 'system', category?: string): Promise<SettingDefinition[]> {
    let query = this.supabase
      .from('setting_definitions')
      .select('*')
      .eq('is_active', true);

    if (scope) {
      query = query.eq('scope', scope);
    }

    if (category) {
      query = query.eq('category', category);
    }

    query = query.order('display_order');

    const { data, error } = await query;
    if (error) throw error;

    return data || [];
  }

  // Notification Preferences
  async getNotificationPreferences(businessId?: string): Promise<NotificationPreference[]> {
    const user = await this.supabase.auth.getUser();
    if (!user.data.user) throw new Error('User not authenticated');

    let query = this.supabase
      .from('notification_preferences')
      .select('*')
      .eq('user_id', user.data.user.id);

    if (businessId) {
      query = query.eq('business_id', businessId);
    }

    const { data, error } = await query;
    if (error) throw error;

    return data || [];
  }

  async updateNotificationPreference(
    notificationType: string,
    preferences: Partial<NotificationPreference>,
    businessId?: string
  ): Promise<void> {
    const user = await this.supabase.auth.getUser();
    if (!user.data.user) throw new Error('User not authenticated');

    const { error } = await this.supabase
      .from('notification_preferences')
      .upsert({
        user_id: user.data.user.id,
        business_id: businessId,
        notification_type: notificationType,
        ...preferences,
        updated_at: new Date().toISOString(),
      });

    if (error) throw error;
  }

  // Email Templates
  async getEmailTemplates(businessId: string, templateType?: string): Promise<EmailTemplate[]> {
    let query = this.supabase
      .from('email_templates')
      .select('*')
      .eq('business_id', businessId)
      .eq('is_active', true);

    if (templateType) {
      query = query.eq('template_type', templateType);
    }

    query = query.order('name');

    const { data, error } = await query;
    if (error) throw error;

    return data || [];
  }

  async createEmailTemplate(businessId: string, templateData: Partial<EmailTemplate>): Promise<EmailTemplate> {
    const user = await this.supabase.auth.getUser();

    const { data, error } = await this.supabase
      .from('email_templates')
      .insert({
        business_id: businessId,
        ...templateData,
        created_by: user.data.user?.id,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async updateEmailTemplate(templateId: string, updates: Partial<EmailTemplate>): Promise<EmailTemplate> {
    const user = await this.supabase.auth.getUser();

    const { data, error } = await this.supabase
      .from('email_templates')
      .update({
        ...updates,
        updated_by: user.data.user?.id,
        updated_at: new Date().toISOString(),
      })
      .eq('id', templateId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  // Tax Configurations
  async getTaxConfigurations(businessId: string): Promise<TaxConfiguration[]> {
    const { data, error } = await this.supabase
      .from('tax_configurations')
      .select('*')
      .eq('business_id', businessId)
      .order('tax_name');

    if (error) throw error;
    return data || [];
  }

  async createTaxConfiguration(businessId: string, taxData: Partial<TaxConfiguration>): Promise<TaxConfiguration> {
    const user = await this.supabase.auth.getUser();

    const { data, error } = await this.supabase
      .from('tax_configurations')
      .insert({
        business_id: businessId,
        ...taxData,
        created_by: user.data.user?.id,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async updateTaxConfiguration(taxId: string, updates: Partial<TaxConfiguration>): Promise<TaxConfiguration> {
    const { data, error } = await this.supabase
      .from('tax_configurations')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', taxId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  // System Configurations
  async getSystemConfigurations(category?: string): Promise<Record<string, any>> {
    let query = this.supabase
      .from('system_configurations')
      .select('category, key, value, data_type')
      .eq('is_public', true);

    if (category) {
      query = query.eq('category', category);
    }

    const { data, error } = await query;
    if (error) throw error;

    return this.flattenSettings(data || []);
  }

  // Utility Methods
  async validateSetting(category: string, key: string, value: any): Promise<boolean> {
    const { data: definition } = await this.supabase
      .from('setting_definitions')
      .select('validation_rules, data_type, possible_values')
      .eq('category', category)
      .eq('key', key)
      .single();

    if (!definition) return true; // No validation rules defined

    // Basic type validation
    if (!this.validateDataType(value, definition.data_type)) {
      return false;
    }

    // Possible values validation
    if (definition.possible_values && !definition.possible_values.includes(value)) {
      return false;
    }

    // JSON schema validation
    if (definition.validation_rules) {
      return this.validateAgainstSchema(value, definition.validation_rules);
    }

    return true;
  }

  async exportSettings(businessId: string, format: 'json' | 'csv'): Promise<Blob> {
    const settings = await this.getBusinessSettings(businessId);
    
    if (format === 'json') {
      return new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' });
    } else {
      // Convert to CSV format
      const csv = this.convertSettingsToCSV(settings);
      return new Blob([csv], { type: 'text/csv' });
    }
  }

  async importSettings(businessId: string, file: File): Promise<{ imported: number; errors: string[] }> {
    const text = await file.text();
    let settings: any;

    try {
      if (file.name.endsWith('.json')) {
        settings = JSON.parse(text);
      } else if (file.name.endsWith('.csv')) {
        settings = this.parseCSVSettings(text);
      } else {
        throw new Error('Unsupported file format');
      }
    } catch (error) {
      return { imported: 0, errors: ['Invalid file format'] };
    }

    const errors: string[] = [];
    let imported = 0;

    for (const [category, categorySettings] of Object.entries(settings)) {
      for (const [key, value] of Object.entries(categorySettings as any)) {
        try {
          const isValid = await this.validateSetting(category, key, value);
          if (isValid) {
            await this.updateBusinessSetting(businessId, category, key, value);
            imported++;
          } else {
            errors.push(`Invalid value for ${category}.${key}`);
          }
        } catch (error) {
          errors.push(`Error importing ${category}.${key}: ${error.message}`);
        }
      }
    }

    return { imported, errors };
  }

  private flattenSettings(settings: any[]): Record<string, any> {
    const result: Record<string, any> = {};
    
    for (const setting of settings) {
      if (!result[setting.category]) {
        result[setting.category] = {};
      }
      result[setting.category][setting.key] = setting.value;
    }
    
    return result;
  }

  private validateDataType(value: any, dataType: string): boolean {
    switch (dataType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'json':
        return typeof value === 'object';
      case 'array':
        return Array.isArray(value);
      case 'date':
        return !isNaN(Date.parse(value));
      case 'email':
        return typeof value === 'string' && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
      case 'url':
        try {
          new URL(value);
          return true;
        } catch {
          return false;
        }
      default:
        return true;
    }
  }

  private validateAgainstSchema(value: any, schema: any): boolean {
    // Implement JSON schema validation
    // This would typically use a library like Ajv
    return true;
  }

  private convertSettingsToCSV(settings: Record<string, any>): string {
    const rows = ['Category,Key,Value,Type'];
    
    for (const [category, categorySettings] of Object.entries(settings)) {
      for (const [key, value] of Object.entries(categorySettings)) {
        const type = typeof value;
        const csvValue = typeof value === 'object' ? JSON.stringify(value) : String(value);
        rows.push(`${category},${key},"${csvValue}",${type}`);
      }
    }
    
    return rows.join('\n');
  }

  private parseCSVSettings(csv: string): Record<string, any> {
    const lines = csv.split('\n');
    const settings: Record<string, any> = {};
    
    // Skip header row
    for (let i = 1; i < lines.length; i++) {
      const [category, key, value, type] = lines[i].split(',');
      
      if (!category || !key) continue;
      
      if (!settings[category]) {
        settings[category] = {};
      }
      
      // Parse value based on type
      let parsedValue = value.replace(/^"|"$/g, ''); // Remove quotes
      if (type === 'number') {
        parsedValue = parseFloat(parsedValue);
      } else if (type === 'boolean') {
        parsedValue = parsedValue === 'true';
      } else if (type === 'object') {
        try {
          parsedValue = JSON.parse(parsedValue);
        } catch {
          // Keep as string if JSON parsing fails
        }
      }
      
      settings[category][key] = parsedValue;
    }
    
    return settings;
  }
}
```

## Security Measures
- **Setting Validation**: Comprehensive validation rules and type checking
- **Access Control**: Role-based permissions for different setting categories
- **Audit Trail**: Complete tracking of setting changes with versioning
- **Data Encryption**: Sensitive settings encrypted at rest
- **Input Sanitization**: Proper sanitization of all setting values
- **Schema Validation**: JSON schema validation for complex settings

## Integration Points
- **All Modules**: Settings provide configuration for all business operations
- **User Management**: User-specific preferences and settings
- **Business Management**: Business-wide configuration and preferences
- **Email Service**: Email template and notification settings
- **Tax Management**: Tax configuration and compliance settings

## Error Handling & Validation
- **Setting Validation**: Comprehensive validation against defined schemas
- **Type Safety**: Strong typing and data type validation
- **Default Values**: Fallback to default values for missing settings
- **Import Validation**: Robust validation during settings import
- **Error Recovery**: Graceful handling of invalid or corrupted settings

## Testing Strategy
- **Unit Tests**: Settings service methods and validation logic
- **Integration Tests**: Settings persistence and retrieval
- **Validation Tests**: Setting validation rules and constraints
- **Import/Export Tests**: Settings import and export functionality
- **Security Tests**: Access control and permission validation

## Implementation Tasks
1. **Database Schema Setup**
   - Create settings-related tables and indexes
   - Implement RLS policies for secure access
   - Set up setting definitions and validation rules

2. **Settings Service Development**
   - Build SettingsService with all CRUD operations
   - Implement validation and type checking
   - Create import/export functionality

3. **Frontend Components**
   - Build settings management interface
   - Create dynamic form generation from definitions
   - Implement settings categories and organization

4. **Notification System**
   - Build notification preference management
   - Create email template editor
   - Implement notification delivery settings

5. **Tax Configuration**
   - Build tax configuration interface
   - Implement tax calculation settings
   - Create compliance configuration options

6. **Advanced Features**
   - Build settings versioning and rollback
   - Implement settings templates and presets
   - Create bulk settings operations

## Dependencies
- **Business Management**: Business context for settings
- **User Management**: User-specific settings and preferences
- **Email Service**: Email template and notification configuration
- **Tax Management**: Tax configuration and compliance settings
- **Audit Logging**: Settings change tracking and versioning

## Success Criteria
- ✅ All settings can be configured and managed easily
- ✅ Setting validation prevents invalid configurations
- ✅ User preferences are saved and applied correctly
- ✅ Email templates can be customized and used
- ✅ Tax configurations work accurately
- ✅ Settings import/export functionality works reliably
- ✅ All settings are properly scoped and secured
- ✅ Settings interface is intuitive and user-friendly
