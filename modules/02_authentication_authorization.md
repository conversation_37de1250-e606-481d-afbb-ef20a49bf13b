# Authentication & Authorization

## Overview
This module implements a comprehensive authentication and authorization system using Supabase Auth, providing secure user registration, login, password management, and role-based access control. It ensures that users can only access resources they're authorized to view and modify within their business context.

## Core Functionalities
- User registration and email verification
- Secure login/logout with JWT tokens
- Password reset and change functionality
- Multi-factor authentication (MFA) support
- Role-based access control (RBAC)
- Session management and automatic token refresh
- Social authentication (Google, GitHub, etc.)
- Business invitation and onboarding flow

## Technical Specifications

### Authentication Flow
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant S as Supabase Auth
    participant D as Database
    
    U->>F: Register/Login
    F->>S: Auth request
    S->>S: Validate credentials
    S->>D: Create/verify user
    S->>F: JWT token + refresh token
    F->>F: Store tokens securely
    F->>U: Redirect to dashboard
    
    Note over F,S: Automatic token refresh
    F->>S: Refresh token
    S->>F: New JWT token
```

### Database Schema Extensions
```sql
-- Extend users table with additional auth fields
ALTER TABLE app.users ADD COLUMN IF NOT EXISTS
    email_verified_at TIMESTAMPTZ,
    phone_verified_at TIMESTAMPTZ,
    mfa_enabled BOOLEAN DEFAULT FALSE,
    mfa_secret TEXT,
    last_password_change TIMESTAMPTZ,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMPTZ,
    password_reset_token TEXT,
    password_reset_expires TIMESTAMPTZ;

-- User sessions table for tracking active sessions
CREATE TABLE app.user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES app.users(id) ON DELETE CASCADE,
    session_token TEXT NOT NULL,
    device_info JSONB,
    ip_address INET,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    last_activity_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Business invitations table
CREATE TABLE app.business_invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    role app.user_role NOT NULL DEFAULT 'user',
    invited_by UUID NOT NULL REFERENCES app.users(id),
    invitation_token TEXT NOT NULL UNIQUE,
    expires_at TIMESTAMPTZ NOT NULL,
    accepted_at TIMESTAMPTZ,
    accepted_by UUID REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(business_id, email)
);

-- Permissions table for granular access control
CREATE TABLE app.permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    resource TEXT NOT NULL,
    action TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Role permissions mapping
CREATE TABLE app.role_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    role app.user_role NOT NULL,
    permission_id UUID NOT NULL REFERENCES app.permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(role, permission_id)
);

-- Indexes
CREATE INDEX idx_user_sessions_user_id ON app.user_sessions(user_id);
CREATE INDEX idx_user_sessions_active ON app.user_sessions(is_active, expires_at);
CREATE INDEX idx_business_invitations_token ON app.business_invitations(invitation_token);
CREATE INDEX idx_business_invitations_email ON app.business_invitations(email);

-- Enable RLS
ALTER TABLE app.user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.business_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.role_permissions ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own sessions"
ON app.user_sessions FOR ALL
USING (user_id = auth.uid());

CREATE POLICY "Business admins can manage invitations"
ON app.business_invitations FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() 
        AND role IN ('owner', 'admin')
        AND is_active = TRUE
    )
);
```

### Authentication Service
```typescript
// lib/auth.ts
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { User, Session } from '@supabase/supabase-js';

export class AuthService {
  private supabase = createClientComponentClient();

  async signUp(email: string, password: string, userData: {
    firstName: string;
    lastName: string;
    businessName?: string;
  }) {
    const { data, error } = await this.supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          first_name: userData.firstName,
          last_name: userData.lastName,
          business_name: userData.businessName,
        },
        emailRedirectTo: `${window.location.origin}/auth/callback`,
      },
    });

    if (error) throw error;
    return data;
  }

  async signIn(email: string, password: string) {
    const { data, error } = await this.supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;
    
    // Track login
    await this.trackLogin(data.user);
    return data;
  }

  async signOut() {
    const { error } = await this.supabase.auth.signOut();
    if (error) throw error;
  }

  async resetPassword(email: string) {
    const { error } = await this.supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`,
    });
    if (error) throw error;
  }

  async updatePassword(newPassword: string) {
    const { error } = await this.supabase.auth.updateUser({
      password: newPassword,
    });
    if (error) throw error;
  }

  async enableMFA() {
    const { data, error } = await this.supabase.auth.mfa.enroll({
      factorType: 'totp',
    });
    if (error) throw error;
    return data;
  }

  private async trackLogin(user: User) {
    await this.supabase.from('user_sessions').insert({
      user_id: user.id,
      device_info: this.getDeviceInfo(),
      ip_address: await this.getClientIP(),
      user_agent: navigator.userAgent,
    });
  }

  private getDeviceInfo() {
    return {
      platform: navigator.platform,
      language: navigator.language,
      screen: {
        width: screen.width,
        height: screen.height,
      },
    };
  }

  private async getClientIP(): Promise<string> {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch {
      return 'unknown';
    }
  }
}
```

### Permission System
```typescript
// lib/permissions.ts
export enum Resource {
  BUSINESS = 'business',
  USER = 'user',
  CUSTOMER = 'customer',
  PRODUCT = 'product',
  INVOICE = 'invoice',
  PAYMENT = 'payment',
  REPORT = 'report',
  SETTING = 'setting',
}

export enum Action {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  MANAGE = 'manage',
}

export const ROLE_PERMISSIONS = {
  owner: ['*:*'], // Full access
  admin: [
    'business:read',
    'user:*',
    'customer:*',
    'product:*',
    'invoice:*',
    'payment:*',
    'report:read',
    'setting:*',
  ],
  manager: [
    'business:read',
    'user:read',
    'customer:*',
    'product:*',
    'invoice:*',
    'payment:read',
    'report:read',
    'setting:read',
  ],
  user: [
    'business:read',
    'customer:read',
    'product:read',
    'invoice:create',
    'invoice:read',
    'invoice:update',
    'payment:read',
    'report:read',
  ],
  viewer: [
    'business:read',
    'customer:read',
    'product:read',
    'invoice:read',
    'payment:read',
    'report:read',
  ],
};

export function hasPermission(
  userRole: string,
  resource: Resource,
  action: Action
): boolean {
  const permissions = ROLE_PERMISSIONS[userRole as keyof typeof ROLE_PERMISSIONS] || [];
  
  return permissions.some(permission => {
    const [permResource, permAction] = permission.split(':');
    return (
      (permResource === '*' || permResource === resource) &&
      (permAction === '*' || permAction === action)
    );
  });
}
```

### React Hooks for Authentication
```typescript
// hooks/useAuth.ts
import { useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { AuthService } from '@/lib/auth';

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const authService = new AuthService();

  useEffect(() => {
    // Get initial session
    authService.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    // Listen for auth changes
    const { data: { subscription } } = authService.onAuthStateChange(
      (event, session) => {
        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  return {
    user,
    session,
    loading,
    signIn: authService.signIn.bind(authService),
    signUp: authService.signUp.bind(authService),
    signOut: authService.signOut.bind(authService),
    resetPassword: authService.resetPassword.bind(authService),
  };
}

// hooks/usePermissions.ts
export function usePermissions() {
  const { user } = useAuth();
  const [userRole, setUserRole] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      // Fetch user's role in current business context
      fetchUserRole(user.id).then(setUserRole);
    }
  }, [user]);

  const can = (resource: Resource, action: Action) => {
    if (!userRole) return false;
    return hasPermission(userRole, resource, action);
  };

  return { can, userRole };
}
```

## Security Measures
- **JWT Token Security**: Secure token storage and automatic refresh
- **Password Policies**: Strong password requirements and validation
- **Rate Limiting**: Login attempt limiting and account lockout
- **Session Management**: Secure session tracking and invalidation
- **MFA Support**: Time-based one-time password (TOTP) authentication
- **Email Verification**: Required email verification for new accounts
- **Invitation Security**: Secure token-based business invitations
- **Permission Validation**: Server-side permission checks on all operations

## Integration Points
- **Core Infrastructure**: Database schema and RLS policies
- **Business Management**: Role assignment and business context
- **User Management**: User profile and preference management
- **All Modules**: Permission-based access control throughout application

## Error Handling & Validation
- **Authentication Errors**: Clear error messages for login failures
- **Validation Schemas**: Zod schemas for all auth-related inputs
- **Session Expiry**: Graceful handling of expired sessions
- **Network Errors**: Retry logic for transient network issues
- **Security Violations**: Logging and alerting for suspicious activities

## Testing Strategy
- **Unit Tests**: Authentication service and permission functions
- **Integration Tests**: Auth flow with Supabase backend
- **E2E Tests**: Complete registration and login workflows
- **Security Tests**: Permission bypass attempts and session security
- **Load Tests**: Authentication system under high load

## Implementation Tasks
1. **Supabase Auth Configuration**
   - Configure auth providers and settings
   - Set up email templates and redirects
   - Configure MFA and security policies

2. **Authentication Service Implementation**
   - Build AuthService class with all auth methods
   - Implement secure token storage
   - Add session management and tracking

3. **Permission System Development**
   - Create permission definitions and mappings
   - Implement role-based access control
   - Build permission checking utilities

4. **React Integration**
   - Create authentication hooks and context
   - Build auth-related UI components
   - Implement protected route components

5. **Business Invitation System**
   - Build invitation creation and management
   - Implement secure invitation acceptance flow
   - Create invitation email templates

6. **Security Hardening**
   - Implement rate limiting and account lockout
   - Add security monitoring and alerting
   - Configure security headers and policies

## Dependencies
- **Core Infrastructure**: Database schema and RLS foundation
- **External Services**: Supabase Auth, email service
- **Frontend Components**: Form components and UI library
- **Monitoring**: Error tracking and security monitoring

## Success Criteria
- ✅ Users can register and verify email addresses
- ✅ Secure login/logout with proper session management
- ✅ Role-based permissions work correctly across all modules
- ✅ MFA can be enabled and used successfully
- ✅ Business invitations work end-to-end
- ✅ All security measures are properly implemented
- ✅ Authentication system handles edge cases gracefully
