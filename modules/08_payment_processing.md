# Payment Processing & Tracking

## Overview
This module handles all payment-related functionality including payment recording, tracking, reconciliation, and integration with payment processors like Stripe. It provides comprehensive payment management with support for multiple payment methods, partial payments, refunds, and automated payment workflows.

## Core Functionalities
- Payment recording and tracking against invoices
- Multiple payment methods support (credit card, bank transfer, cash, check)
- Partial payment handling and payment plans
- Payment reconciliation and matching
- Refund processing and tracking
- Stripe integration for online payments
- Payment reminders and automated workflows
- Payment analytics and reporting
- Currency conversion and multi-currency support
- Payment gateway webhook handling

## Technical Specifications

### Database Schema
```sql
-- Payment methods enum (Local Bangladesh methods prioritized, international methods available)
CREATE TYPE app.payment_method AS ENUM (
    -- Local Bangladesh methods (Phase 1 priority)
    'bkash_personal',    -- bKash personal account transfer
    'bkash_merchant',    -- bKash merchant API
    'nagad_personal',    -- Nagad personal account transfer
    'nagad_merchant',    -- Nagad merchant API
    'bank_transfer',     -- Manual bank-to-bank transfer
    'cash',              -- In-person cash payment

    -- International methods (available for future use)
    'credit_card',       -- Credit card payments
    'debit_card',        -- Debit card payments
    'stripe',            -- Stripe payment processor
    'ach',               -- ACH bank transfers
    'wire',              -- Wire transfers
    'check',             -- Check payments
    'other'              -- Other payment methods
);

-- Payment status enum
CREATE TYPE app.payment_status AS ENUM (
    'pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded', 'partially_refunded'
);

-- Main payments table
CREATE TABLE app.payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    invoice_id UUID NOT NULL REFERENCES app.invoices(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES app.customers(id) ON DELETE CASCADE,
    
    -- Payment details
    payment_number TEXT NOT NULL,
    amount NUMERIC(12, 2) NOT NULL CHECK (amount > 0),
    currency TEXT NOT NULL DEFAULT 'BDT', -- Bangladeshi Taka
    exchange_rate NUMERIC(10, 6) DEFAULT 1.0,
    amount_in_base_currency NUMERIC(12, 2) NOT NULL,
    
    -- Payment method and status
    payment_method app.payment_method NOT NULL,
    payment_status app.payment_status NOT NULL DEFAULT 'pending',
    
    -- Dates
    payment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    processed_at TIMESTAMPTZ,
    
    -- External references
    external_transaction_id TEXT, -- bKash/Nagad transaction ID, Stripe payment intent, bank reference, etc.
    gateway_response JSONB, -- Full gateway response for debugging

    -- Payment method specific details (stored as JSONB for flexibility)
    payment_details JSONB, -- Method-specific details like mobile numbers, verification status, etc.

    -- Additional details
    reference_number TEXT, -- Check number, wire reference, etc.
    notes TEXT,
    
    -- Fees
    processing_fee NUMERIC(12, 2) DEFAULT 0,
    gateway_fee NUMERIC(12, 2) DEFAULT 0,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, payment_number)
);

-- Payment allocations (for partial payments across multiple invoices)
CREATE TABLE app.payment_allocations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    payment_id UUID NOT NULL REFERENCES app.payments(id) ON DELETE CASCADE,
    invoice_id UUID NOT NULL REFERENCES app.invoices(id) ON DELETE CASCADE,
    
    allocated_amount NUMERIC(12, 2) NOT NULL CHECK (allocated_amount > 0),
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Refunds table
CREATE TABLE app.refunds (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    payment_id UUID NOT NULL REFERENCES app.payments(id) ON DELETE CASCADE,
    
    refund_number TEXT NOT NULL,
    amount NUMERIC(12, 2) NOT NULL CHECK (amount > 0),
    reason TEXT,
    
    -- External references
    external_refund_id TEXT, -- Stripe refund ID, etc.
    gateway_response JSONB,
    
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    processed_at TIMESTAMPTZ,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Payment reminders
CREATE TABLE app.payment_reminders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    invoice_id UUID NOT NULL REFERENCES app.invoices(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES app.customers(id) ON DELETE CASCADE,
    
    reminder_type TEXT NOT NULL CHECK (reminder_type IN ('first', 'second', 'final', 'custom')),
    days_overdue INTEGER NOT NULL,
    
    sent_at TIMESTAMPTZ,
    email_subject TEXT,
    email_content TEXT,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Payment gateway configurations
CREATE TABLE app.payment_gateways (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    gateway_name TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE,
    
    -- Encrypted configuration
    configuration JSONB NOT NULL, -- API keys, webhook secrets, etc.
    
    -- Settings
    supported_currencies TEXT[] DEFAULT ARRAY['BDT', 'USD'],
    supported_methods app.payment_method[] DEFAULT ARRAY['bkash_merchant', 'nagad_merchant', 'credit_card', 'stripe'],
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, gateway_name)
);

-- Webhook events from payment gateways
CREATE TABLE app.payment_webhooks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    gateway_name TEXT NOT NULL,
    event_type TEXT NOT NULL,
    event_id TEXT NOT NULL, -- External event ID
    
    payload JSONB NOT NULL,
    processed BOOLEAN DEFAULT FALSE,
    processed_at TIMESTAMPTZ,
    error_message TEXT,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(gateway_name, event_id)
);

-- Indexes for performance
CREATE INDEX idx_payments_business_id ON app.payments(business_id);
CREATE INDEX idx_payments_invoice_id ON app.payments(invoice_id);
CREATE INDEX idx_payments_customer_id ON app.payments(customer_id);
CREATE INDEX idx_payments_status ON app.payments(payment_status);
CREATE INDEX idx_payments_date ON app.payments(payment_date);
CREATE INDEX idx_payments_external_id ON app.payments(external_transaction_id);
CREATE INDEX idx_payment_allocations_payment_id ON app.payment_allocations(payment_id);
CREATE INDEX idx_payment_allocations_invoice_id ON app.payment_allocations(invoice_id);
CREATE INDEX idx_refunds_payment_id ON app.refunds(payment_id);
CREATE INDEX idx_payment_reminders_invoice_id ON app.payment_reminders(invoice_id);
CREATE INDEX idx_payment_reminders_sent_at ON app.payment_reminders(sent_at);
CREATE INDEX idx_payments_payment_details ON app.payments USING GIN (payment_details); -- For JSONB queries

/*
Example payment_details JSONB structure for different payment methods:

bKash/Nagad personal:
{
  "sender_number": "+8801XXXXXXXXX",
  "receiver_number": "+8801XXXXXXXXX",
  "verification_status": "pending|verified|failed",
  "verification_method": "manual|screenshot|sms",
  "verification_notes": "...",
  "verified_by": "user_id",
  "verified_at": "timestamp"
}

bKash/Nagad merchant:
{
  "sender_number": "+8801XXXXXXXXX",
  "api_response": {...},
  "api_status": "success|failed"
}

Stripe:
{
  "payment_intent_id": "pi_...",
  "customer_id": "cus_...",
  "payment_method_id": "pm_..."
}

Credit/Debit Card:
{
  "last_four": "1234",
  "card_brand": "visa|mastercard|...",
  "exp_month": 12,
  "exp_year": 2025
}

Bank Transfer:
{
  "bank_name": "...",
  "account_number": "...",
  "routing_number": "...",
  "verification_status": "pending|verified|failed"
}
*/
CREATE INDEX idx_payment_gateways_business_id ON app.payment_gateways(business_id);
CREATE INDEX idx_payment_webhooks_processed ON app.payment_webhooks(processed, created_at);

-- Enable RLS
ALTER TABLE app.payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.payment_allocations ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.refunds ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.payment_reminders ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.payment_gateways ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.payment_webhooks ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Business members can manage payments"
ON app.payments FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);

CREATE POLICY "Payment data inherits payment access"
ON app.payment_allocations FOR ALL
USING (
    payment_id IN (
        SELECT id FROM app.payments 
        WHERE business_id IN (
            SELECT business_id FROM app.business_users 
            WHERE user_id = auth.uid() AND is_active = TRUE
        )
    )
);

CREATE POLICY "Business owners can manage gateways"
ON app.payment_gateways FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() 
        AND role IN ('owner', 'admin')
        AND is_active = TRUE
    )
);
```

### Payment Service Architecture

**Core Service: PaymentService**
- **Primary Responsibilities**: Multi-method payment processing, mobile banking integration, international gateway support, payment reconciliation
- **Key Methods**:
  - `recordPayment(businessId, paymentData)` - Manual payment recording with invoice allocation
  - `processBkashPayment(businessId, paymentData)` - bKash payment processing (personal & merchant)
  - `processNagadPayment(businessId, paymentData)` - Nagad payment processing (personal & merchant)
  - `processStripePayment(businessId, paymentData)` - Stripe payment processing
  - `recordBankTransfer(businessId, transferData)` - Bank transfer payment recording
  - `recordCashPayment(businessId, cashData)` - Cash payment recording and tracking
  - `processCreditCardPayment(businessId, cardData)` - Credit/debit card processing
  - `getPayment(paymentId)` - Payment retrieval with related data
  - `getPayments(businessId, filters, pagination)` - Payment listing with advanced filtering
  - `createRefund(paymentId, amount, reason)` - Refund processing for all supported methods
  - `sendPaymentReminder(invoiceId, reminderType)` - Automated payment reminder system
  - `reconcilePayments(businessId, dateRange)` - Payment reconciliation and matching

**Implementation Approach**:
- Build comprehensive mobile banking integration for Bangladesh market (bKash, Nagad)
- Implement flexible payment gateway abstraction for future international expansion
- Create robust offline payment tracking and verification system
- Establish payment reminder system with local communication preferences
- Build payment reconciliation tools optimized for mixed payment methods

### Payment Processing Strategy

**Payment Method Prioritization**:
- **Phase 1 Priority (Local Bangladesh)**: bKash, Nagad, bank transfers, cash payments
- **Available International Methods**: Stripe, credit/debit cards, ACH, wire transfers
- **Excluded Methods**: PayPal (removed as requested)
- **Gateway Abstraction**: Flexible architecture supporting all payment processors

**Mobile Banking Integration**:
- **bKash Integration**: Support for both personal account transfers and merchant API
- **Nagad Integration**: Support for both personal account transfers and merchant API
- **Manual Verification**: Process for verifying personal account transfers
- **API Integration**: Automated processing for merchant API transactions
- **Transaction Tracking**: Complete audit trail for all mobile banking transactions

**Offline Payment Management**:
- **Bank Transfer Tracking**: Manual entry and verification of bank transfers
- **Cash Payment Recording**: Point-of-sale cash transaction management
- **Receipt Generation**: Digital receipts for all offline payment methods
- **Verification Workflow**: Multi-step verification process for manual payments
- **Reconciliation Tools**: Automated matching of payments to invoices

## Implementation Tasks

### Phase 1: Core Payment Infrastructure
- [ ] Set up payment database schema with support for all payment methods
- [ ] Implement PaymentService with multi-method support (prioritizing local methods)
- [ ] Create payment status tracking and lifecycle management
- [ ] Build payment allocation system for multi-invoice payments
- [ ] Implement payment number generation with business-specific sequences

### Phase 2: Priority Local Methods (Bangladesh Focus)
- [ ] Set up bKash merchant API integration and webhook handling
- [ ] Set up Nagad merchant API integration and webhook handling
- [ ] Implement personal account transfer verification workflow
- [ ] Build bank transfer recording and verification system
- [ ] Implement cash payment tracking with receipt generation

### Phase 3: International Payment Methods (Available but Lower Priority)
- [ ] Set up Stripe integration and webhook handling
- [ ] Implement credit/debit card processing
- [ ] Build ACH and wire transfer support
- [ ] Create international payment gateway abstraction
- [ ] Implement multi-currency exchange rate handling

### Phase 4: Advanced Payment Features
- [ ] Build payment reminder system with SMS/email support
- [ ] Implement payment reconciliation tools for all payment methods
- [ ] Create payment analytics dashboard
- [ ] Build payment export functionality for accounting software
- [ ] Implement payment search and filtering capabilities

### Phase 5: Business Optimization
- [ ] Add support for BDT and international currency calculations
- [ ] Create customer payment history and credit tracking
- [ ] Implement advanced payment analytics with forecasting
- [ ] Build payment automation rules and workflows
- [ ] Create payment method configuration management

## Security Measures
- **Payment Data Encryption**: Sensitive payment information encrypted at rest
- **Mobile Banking Security**: Secure API key management for bKash/Nagad integrations
- **Transaction Verification**: Multi-step verification for personal account transfers
- **Fraud Prevention**: Transaction monitoring and risk assessment for local payments
- **Access Control**: Role-based permissions for payment operations and verification
- **Audit Trail**: Complete logging of all payment activities and verification steps
- **Data Privacy**: Secure handling of mobile numbers and transaction details

## Integration Points
- **Invoice Management**: Payment allocation and invoice status updates
- **Customer Management**: Customer payment history and preferences
- **Business Management**: Business-scoped payment data and settings
- **Reporting & Analytics**: Payment analytics and financial reporting
- **Email Service**: Payment confirmations and reminder notifications
- **SMS Service**: Mobile banking transaction notifications and verification

## Future Expansion Considerations

### International Payment Gateway Integration
The payment architecture is designed with flexibility to accommodate future international payment processors:

- **Gateway Abstraction Layer**: Pluggable architecture allows easy addition of Stripe, PayPal, Square, etc.
- **Multi-Currency Support**: Framework ready for USD, EUR, and other international currencies
- **Payment Method Expansion**: Easy addition of credit cards, digital wallets, and other payment methods
- **Compliance Framework**: Extensible compliance system for PCI DSS and international regulations

### Scalability Considerations
- **Database Optimization**: Indexes and partitioning strategies for high transaction volumes
- **API Rate Limiting**: Built-in rate limiting for mobile banking API integrations
- **Caching Strategy**: Payment data caching for improved performance
- **Background Processing**: Asynchronous processing for payment verification and reconciliation

### Regional Adaptation
- **Local Payment Methods**: Framework supports addition of region-specific payment methods
- **Currency Localization**: Support for local currency formatting and calculations
- **Regulatory Compliance**: Adaptable compliance framework for different jurisdictions
- **Language Support**: Multi-language support for payment interfaces and notifications

## Error Handling & Validation
- **Payment Validation**: Amount, currency, and method validation
- **Gateway Error Handling**: Graceful handling of payment gateway failures
- **Webhook Security**: Signature verification and replay protection
- **Refund Validation**: Refund amount and eligibility checks
- **Currency Validation**: Exchange rate and currency conversion

## Testing Strategy
- **Unit Tests**: Payment calculations and business logic
- **Integration Tests**: Payment gateway integration and webhooks
- **E2E Tests**: Complete payment workflows
- **Security Tests**: Payment security and fraud prevention
- **Performance Tests**: High-volume payment processing

## Implementation Tasks
1. **Database Schema Setup**
   - Create payment-related tables and indexes
   - Implement RLS policies for data security
   - Set up webhook event tracking

2. **Payment Service Development**
   - Build PaymentService with all operations
   - Implement Stripe integration
   - Create webhook handling system

3. **Frontend Components**
   - Build payment recording interface
   - Create payment history and details views
   - Implement Stripe payment forms

4. **Gateway Integration**
   - Set up Stripe webhook endpoints
   - Implement payment gateway configuration
   - Build gateway management interface

5. **Automated Workflows**
   - Build payment reminder system
   - Implement automated reconciliation
   - Create payment analytics

6. **Advanced Features**
   - Build refund processing system
   - Implement multi-currency support
   - Create payment plan functionality

## Dependencies
- **Invoice Management**: Invoice data and status updates
- **Customer Management**: Customer payment information
- **Business Management**: Business payment settings
- **Email Service**: Payment notifications and reminders
- **External Services**: Stripe, currency exchange APIs

## Success Criteria
- ✅ Payments can be recorded and tracked accurately
- ✅ Stripe integration works seamlessly
- ✅ Webhook processing is reliable and secure
- ✅ Refund processing works correctly
- ✅ Payment reminders are sent automatically
- ✅ All payment calculations are accurate
- ✅ Payment security measures are properly implemented
- ✅ Multi-currency support works correctly
