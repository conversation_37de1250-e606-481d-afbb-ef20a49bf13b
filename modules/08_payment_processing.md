# Payment Processing & Tracking

## Overview
This module handles all payment-related functionality including payment recording, tracking, reconciliation, and integration with payment processors like Stripe. It provides comprehensive payment management with support for multiple payment methods, partial payments, refunds, and automated payment workflows.

## Core Functionalities
- Payment recording and tracking against invoices
- Multiple payment methods support (credit card, bank transfer, cash, check)
- Partial payment handling and payment plans
- Payment reconciliation and matching
- Refund processing and tracking
- Stripe integration for online payments
- Payment reminders and automated workflows
- Payment analytics and reporting
- Currency conversion and multi-currency support
- Payment gateway webhook handling

## Technical Specifications

### Database Schema
```sql
-- Payment methods enum
CREATE TYPE app.payment_method AS ENUM (
    'credit_card', 'debit_card', 'bank_transfer', 'ach', 'wire', 
    'cash', 'check', 'paypal', 'stripe', 'other'
);

-- Payment status enum
CREATE TYPE app.payment_status AS ENUM (
    'pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded', 'partially_refunded'
);

-- Main payments table
CREATE TABLE app.payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    invoice_id UUID NOT NULL REFERENCES app.invoices(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES app.customers(id) ON DELETE CASCADE,
    
    -- Payment details
    payment_number TEXT NOT NULL,
    amount NUMERIC(12, 2) NOT NULL CHECK (amount > 0),
    currency TEXT NOT NULL DEFAULT 'USD',
    exchange_rate NUMERIC(10, 6) DEFAULT 1.0,
    amount_in_base_currency NUMERIC(12, 2) NOT NULL,
    
    -- Payment method and status
    payment_method app.payment_method NOT NULL,
    payment_status app.payment_status NOT NULL DEFAULT 'pending',
    
    -- Dates
    payment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    processed_at TIMESTAMPTZ,
    
    -- External references
    external_transaction_id TEXT, -- Stripe payment intent ID, bank reference, etc.
    gateway_response JSONB, -- Full gateway response for debugging
    
    -- Additional details
    reference_number TEXT, -- Check number, wire reference, etc.
    notes TEXT,
    
    -- Fees
    processing_fee NUMERIC(12, 2) DEFAULT 0,
    gateway_fee NUMERIC(12, 2) DEFAULT 0,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, payment_number)
);

-- Payment allocations (for partial payments across multiple invoices)
CREATE TABLE app.payment_allocations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    payment_id UUID NOT NULL REFERENCES app.payments(id) ON DELETE CASCADE,
    invoice_id UUID NOT NULL REFERENCES app.invoices(id) ON DELETE CASCADE,
    
    allocated_amount NUMERIC(12, 2) NOT NULL CHECK (allocated_amount > 0),
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Refunds table
CREATE TABLE app.refunds (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    payment_id UUID NOT NULL REFERENCES app.payments(id) ON DELETE CASCADE,
    
    refund_number TEXT NOT NULL,
    amount NUMERIC(12, 2) NOT NULL CHECK (amount > 0),
    reason TEXT,
    
    -- External references
    external_refund_id TEXT, -- Stripe refund ID, etc.
    gateway_response JSONB,
    
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    processed_at TIMESTAMPTZ,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Payment reminders
CREATE TABLE app.payment_reminders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    invoice_id UUID NOT NULL REFERENCES app.invoices(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES app.customers(id) ON DELETE CASCADE,
    
    reminder_type TEXT NOT NULL CHECK (reminder_type IN ('first', 'second', 'final', 'custom')),
    days_overdue INTEGER NOT NULL,
    
    sent_at TIMESTAMPTZ,
    email_subject TEXT,
    email_content TEXT,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Payment gateway configurations
CREATE TABLE app.payment_gateways (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    gateway_name TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE,
    
    -- Encrypted configuration
    configuration JSONB NOT NULL, -- API keys, webhook secrets, etc.
    
    -- Settings
    supported_currencies TEXT[] DEFAULT ARRAY['USD'],
    supported_methods app.payment_method[] DEFAULT ARRAY['credit_card'],
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, gateway_name)
);

-- Webhook events from payment gateways
CREATE TABLE app.payment_webhooks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    gateway_name TEXT NOT NULL,
    event_type TEXT NOT NULL,
    event_id TEXT NOT NULL, -- External event ID
    
    payload JSONB NOT NULL,
    processed BOOLEAN DEFAULT FALSE,
    processed_at TIMESTAMPTZ,
    error_message TEXT,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(gateway_name, event_id)
);

-- Indexes for performance
CREATE INDEX idx_payments_business_id ON app.payments(business_id);
CREATE INDEX idx_payments_invoice_id ON app.payments(invoice_id);
CREATE INDEX idx_payments_customer_id ON app.payments(customer_id);
CREATE INDEX idx_payments_status ON app.payments(payment_status);
CREATE INDEX idx_payments_date ON app.payments(payment_date);
CREATE INDEX idx_payments_external_id ON app.payments(external_transaction_id);
CREATE INDEX idx_payment_allocations_payment_id ON app.payment_allocations(payment_id);
CREATE INDEX idx_payment_allocations_invoice_id ON app.payment_allocations(invoice_id);
CREATE INDEX idx_refunds_payment_id ON app.refunds(payment_id);
CREATE INDEX idx_payment_reminders_invoice_id ON app.payment_reminders(invoice_id);
CREATE INDEX idx_payment_reminders_sent_at ON app.payment_reminders(sent_at);
CREATE INDEX idx_payment_gateways_business_id ON app.payment_gateways(business_id);
CREATE INDEX idx_payment_webhooks_processed ON app.payment_webhooks(processed, created_at);

-- Enable RLS
ALTER TABLE app.payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.payment_allocations ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.refunds ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.payment_reminders ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.payment_gateways ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.payment_webhooks ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Business members can manage payments"
ON app.payments FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);

CREATE POLICY "Payment data inherits payment access"
ON app.payment_allocations FOR ALL
USING (
    payment_id IN (
        SELECT id FROM app.payments 
        WHERE business_id IN (
            SELECT business_id FROM app.business_users 
            WHERE user_id = auth.uid() AND is_active = TRUE
        )
    )
);

CREATE POLICY "Business owners can manage gateways"
ON app.payment_gateways FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() 
        AND role IN ('owner', 'admin')
        AND is_active = TRUE
    )
);
```

### Payment Service
```typescript
// lib/payment.ts
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import Stripe from 'stripe';

export interface Payment {
  id: string;
  business_id: string;
  invoice_id: string;
  customer_id: string;
  payment_number: string;
  amount: number;
  currency: string;
  exchange_rate: number;
  amount_in_base_currency: number;
  payment_method: string;
  payment_status: string;
  payment_date: string;
  processed_at?: string;
  external_transaction_id?: string;
  gateway_response?: any;
  reference_number?: string;
  notes?: string;
  processing_fee: number;
  gateway_fee: number;
  created_at: string;
  updated_at: string;
  invoice?: any;
  customer?: any;
  allocations?: PaymentAllocation[];
}

export interface PaymentAllocation {
  id: string;
  payment_id: string;
  invoice_id: string;
  allocated_amount: number;
}

export interface Refund {
  id: string;
  payment_id: string;
  refund_number: string;
  amount: number;
  reason?: string;
  external_refund_id?: string;
  status: string;
  processed_at?: string;
  created_at: string;
}

export interface PaymentGateway {
  id: string;
  business_id: string;
  gateway_name: string;
  is_active: boolean;
  is_default: boolean;
  configuration: any;
  supported_currencies: string[];
  supported_methods: string[];
}

export class PaymentService {
  private supabase = createClientComponentClient();
  private stripe: Stripe | null = null;

  constructor() {
    // Initialize Stripe if API key is available
    const stripeKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
    if (stripeKey) {
      this.stripe = new Stripe(stripeKey, {
        apiVersion: '2023-10-16',
      });
    }
  }

  async recordPayment(businessId: string, paymentData: {
    invoice_id: string;
    amount: number;
    currency?: string;
    payment_method: string;
    payment_date?: string;
    reference_number?: string;
    notes?: string;
    external_transaction_id?: string;
  }): Promise<Payment> {
    // Generate payment number
    const paymentNumber = await this.generatePaymentNumber(businessId);
    
    // Get invoice details
    const { data: invoice } = await this.supabase
      .from('invoices')
      .select('customer_id, currency, total_amount, paid_amount')
      .eq('id', paymentData.invoice_id)
      .single();

    if (!invoice) throw new Error('Invoice not found');

    const currency = paymentData.currency || invoice.currency;
    const exchangeRate = await this.getExchangeRate(currency, 'USD');
    const amountInBaseCurrency = paymentData.amount * exchangeRate;

    // Create payment record
    const { data: payment, error } = await this.supabase
      .from('payments')
      .insert({
        business_id: businessId,
        invoice_id: paymentData.invoice_id,
        customer_id: invoice.customer_id,
        payment_number: paymentNumber,
        amount: paymentData.amount,
        currency,
        exchange_rate: exchangeRate,
        amount_in_base_currency: amountInBaseCurrency,
        payment_method: paymentData.payment_method,
        payment_status: 'completed',
        payment_date: paymentData.payment_date || new Date().toISOString().split('T')[0],
        processed_at: new Date().toISOString(),
        external_transaction_id: paymentData.external_transaction_id,
        reference_number: paymentData.reference_number,
        notes: paymentData.notes,
        created_by: (await this.supabase.auth.getUser()).data.user?.id,
      })
      .select()
      .single();

    if (error) throw error;

    // Create payment allocation
    await this.supabase
      .from('payment_allocations')
      .insert({
        payment_id: payment.id,
        invoice_id: paymentData.invoice_id,
        allocated_amount: paymentData.amount,
      });

    // Update invoice paid amount
    const newPaidAmount = invoice.paid_amount + paymentData.amount;
    const newBalanceDue = invoice.total_amount - newPaidAmount;
    const newStatus = newBalanceDue <= 0 ? 'paid' : 'partial';

    await this.supabase
      .from('invoices')
      .update({
        paid_amount: newPaidAmount,
        balance_due: newBalanceDue,
        status: newStatus,
      })
      .eq('id', paymentData.invoice_id);

    return this.getPayment(payment.id);
  }

  async createStripePaymentIntent(
    businessId: string,
    invoiceId: string,
    amount: number,
    currency = 'usd'
  ): Promise<{ client_secret: string; payment_intent_id: string }> {
    if (!this.stripe) throw new Error('Stripe not configured');

    // Get business Stripe configuration
    const gateway = await this.getPaymentGateway(businessId, 'stripe');
    if (!gateway) throw new Error('Stripe not configured for this business');

    const paymentIntent = await this.stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency,
      metadata: {
        business_id: businessId,
        invoice_id: invoiceId,
      },
    });

    return {
      client_secret: paymentIntent.client_secret!,
      payment_intent_id: paymentIntent.id,
    };
  }

  async handleStripeWebhook(event: Stripe.Event): Promise<void> {
    // Record webhook event
    await this.supabase
      .from('payment_webhooks')
      .insert({
        business_id: event.data.object.metadata?.business_id,
        gateway_name: 'stripe',
        event_type: event.type,
        event_id: event.id,
        payload: event,
      });

    switch (event.type) {
      case 'payment_intent.succeeded':
        await this.handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
        break;
      case 'payment_intent.payment_failed':
        await this.handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent);
        break;
      case 'charge.dispute.created':
        await this.handleChargeDispute(event.data.object as Stripe.Dispute);
        break;
    }

    // Mark webhook as processed
    await this.supabase
      .from('payment_webhooks')
      .update({ processed: true, processed_at: new Date().toISOString() })
      .eq('event_id', event.id);
  }

  async getPayment(paymentId: string): Promise<Payment> {
    const { data, error } = await this.supabase
      .from('payments')
      .select(`
        *,
        invoice:invoices(invoice_number, total_amount),
        customer:customers(name, email),
        allocations:payment_allocations(*)
      `)
      .eq('id', paymentId)
      .single();

    if (error) throw error;
    return data;
  }

  async getPayments(
    businessId: string,
    filters?: {
      invoice_id?: string;
      customer_id?: string;
      status?: string;
      method?: string;
      date_from?: string;
      date_to?: string;
    },
    pagination?: { page: number; limit: number }
  ): Promise<{ payments: Payment[]; total: number }> {
    let query = this.supabase
      .from('payments')
      .select(`
        *,
        invoice:invoices(invoice_number, total_amount),
        customer:customers(name, email)
      `, { count: 'exact' })
      .eq('business_id', businessId);

    // Apply filters
    if (filters?.invoice_id) {
      query = query.eq('invoice_id', filters.invoice_id);
    }
    if (filters?.customer_id) {
      query = query.eq('customer_id', filters.customer_id);
    }
    if (filters?.status) {
      query = query.eq('payment_status', filters.status);
    }
    if (filters?.method) {
      query = query.eq('payment_method', filters.method);
    }
    if (filters?.date_from) {
      query = query.gte('payment_date', filters.date_from);
    }
    if (filters?.date_to) {
      query = query.lte('payment_date', filters.date_to);
    }

    // Apply pagination
    if (pagination) {
      const offset = (pagination.page - 1) * pagination.limit;
      query = query.range(offset, offset + pagination.limit - 1);
    }

    query = query.order('payment_date', { ascending: false });

    const { data, error, count } = await query;
    if (error) throw error;

    return {
      payments: data || [],
      total: count || 0,
    };
  }

  async createRefund(paymentId: string, amount: number, reason?: string): Promise<Refund> {
    const payment = await this.getPayment(paymentId);
    
    // Generate refund number
    const refundNumber = await this.generateRefundNumber(payment.business_id);

    // Process refund through gateway if applicable
    let externalRefundId: string | undefined;
    if (payment.external_transaction_id && payment.payment_method === 'stripe') {
      const stripeRefund = await this.processStripeRefund(
        payment.external_transaction_id,
        amount
      );
      externalRefundId = stripeRefund.id;
    }

    // Create refund record
    const { data: refund, error } = await this.supabase
      .from('refunds')
      .insert({
        payment_id: paymentId,
        refund_number: refundNumber,
        amount,
        reason,
        external_refund_id: externalRefundId,
        status: 'completed',
        processed_at: new Date().toISOString(),
        created_by: (await this.supabase.auth.getUser()).data.user?.id,
      })
      .select()
      .single();

    if (error) throw error;

    // Update payment status
    const totalRefunded = await this.getTotalRefunded(paymentId);
    const newStatus = totalRefunded >= payment.amount ? 'refunded' : 'partially_refunded';
    
    await this.supabase
      .from('payments')
      .update({ payment_status: newStatus })
      .eq('id', paymentId);

    // Update invoice amounts
    await this.updateInvoiceAfterRefund(payment.invoice_id, amount);

    return refund;
  }

  async sendPaymentReminder(invoiceId: string, reminderType: string): Promise<void> {
    const { data: invoice } = await this.supabase
      .from('invoices')
      .select(`
        *,
        customer:customers(*),
        business:businesses(*)
      `)
      .eq('id', invoiceId)
      .single();

    if (!invoice) throw new Error('Invoice not found');

    const daysOverdue = Math.floor(
      (new Date().getTime() - new Date(invoice.due_date).getTime()) / (1000 * 60 * 60 * 24)
    );

    // Create reminder record
    await this.supabase
      .from('payment_reminders')
      .insert({
        business_id: invoice.business_id,
        invoice_id: invoiceId,
        customer_id: invoice.customer_id,
        reminder_type: reminderType,
        days_overdue: daysOverdue,
        sent_at: new Date().toISOString(),
      });

    // Send email reminder (would typically call an Edge Function)
    await fetch('/api/payments/send-reminder', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        invoice,
        reminderType,
        daysOverdue,
      }),
    });
  }

  private async generatePaymentNumber(businessId: string): Promise<string> {
    const { data } = await this.supabase
      .from('payments')
      .select('payment_number')
      .eq('business_id', businessId)
      .order('created_at', { ascending: false })
      .limit(1);

    let nextNumber = 1;
    if (data && data.length > 0) {
      const lastNumber = data[0].payment_number;
      const match = lastNumber.match(/(\d+)$/);
      if (match) {
        nextNumber = parseInt(match[1]) + 1;
      }
    }

    return `PAY-${nextNumber.toString().padStart(6, '0')}`;
  }

  private async generateRefundNumber(businessId: string): Promise<string> {
    const { data } = await this.supabase
      .from('refunds')
      .select('refund_number')
      .order('created_at', { ascending: false })
      .limit(1);

    let nextNumber = 1;
    if (data && data.length > 0) {
      const lastNumber = data[0].refund_number;
      const match = lastNumber.match(/(\d+)$/);
      if (match) {
        nextNumber = parseInt(match[1]) + 1;
      }
    }

    return `REF-${nextNumber.toString().padStart(6, '0')}`;
  }

  private async getExchangeRate(fromCurrency: string, toCurrency: string): Promise<number> {
    if (fromCurrency === toCurrency) return 1.0;
    
    // In a real implementation, you'd call a currency exchange API
    // For now, return 1.0 as a placeholder
    return 1.0;
  }

  private async handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent): Promise<void> {
    const { business_id, invoice_id } = paymentIntent.metadata;
    
    await this.recordPayment(business_id, {
      invoice_id,
      amount: paymentIntent.amount / 100, // Convert from cents
      currency: paymentIntent.currency.toUpperCase(),
      payment_method: 'stripe',
      external_transaction_id: paymentIntent.id,
      notes: 'Payment processed via Stripe',
    });
  }

  private async handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent): Promise<void> {
    // Log failed payment attempt
    console.error('Payment failed:', paymentIntent.last_payment_error);
  }

  private async handleChargeDispute(dispute: Stripe.Dispute): Promise<void> {
    // Handle dispute creation
    console.log('Dispute created:', dispute.id);
  }

  private async processStripeRefund(chargeId: string, amount: number): Promise<Stripe.Refund> {
    if (!this.stripe) throw new Error('Stripe not configured');
    
    return this.stripe.refunds.create({
      charge: chargeId,
      amount: Math.round(amount * 100), // Convert to cents
    });
  }

  private async getTotalRefunded(paymentId: string): Promise<number> {
    const { data } = await this.supabase
      .from('refunds')
      .select('amount')
      .eq('payment_id', paymentId)
      .eq('status', 'completed');

    return data?.reduce((sum, refund) => sum + refund.amount, 0) || 0;
  }

  private async updateInvoiceAfterRefund(invoiceId: string, refundAmount: number): Promise<void> {
    const { data: invoice } = await this.supabase
      .from('invoices')
      .select('paid_amount, total_amount')
      .eq('id', invoiceId)
      .single();

    if (invoice) {
      const newPaidAmount = invoice.paid_amount - refundAmount;
      const newBalanceDue = invoice.total_amount - newPaidAmount;
      const newStatus = newBalanceDue <= 0 ? 'paid' : newBalanceDue >= invoice.total_amount ? 'sent' : 'partial';

      await this.supabase
        .from('invoices')
        .update({
          paid_amount: newPaidAmount,
          balance_due: newBalanceDue,
          status: newStatus,
        })
        .eq('id', invoiceId);
    }
  }

  private async getPaymentGateway(businessId: string, gatewayName: string): Promise<PaymentGateway | null> {
    const { data, error } = await this.supabase
      .from('payment_gateways')
      .select('*')
      .eq('business_id', businessId)
      .eq('gateway_name', gatewayName)
      .eq('is_active', true)
      .single();

    if (error) return null;
    return data;
  }
}
```

## Security Measures
- **Payment Data Encryption**: Sensitive payment information encrypted at rest
- **PCI Compliance**: Secure handling of payment card information
- **Gateway Security**: Secure API key management and webhook validation
- **Fraud Prevention**: Transaction monitoring and risk assessment
- **Access Control**: Role-based permissions for payment operations
- **Audit Trail**: Complete logging of all payment activities

## Integration Points
- **Invoice Management**: Payment allocation and invoice status updates
- **Customer Management**: Customer payment history and preferences
- **Business Management**: Business-scoped payment data and settings
- **Reporting & Analytics**: Payment analytics and financial reporting
- **Email Service**: Payment confirmations and reminder notifications

## Error Handling & Validation
- **Payment Validation**: Amount, currency, and method validation
- **Gateway Error Handling**: Graceful handling of payment gateway failures
- **Webhook Security**: Signature verification and replay protection
- **Refund Validation**: Refund amount and eligibility checks
- **Currency Validation**: Exchange rate and currency conversion

## Testing Strategy
- **Unit Tests**: Payment calculations and business logic
- **Integration Tests**: Payment gateway integration and webhooks
- **E2E Tests**: Complete payment workflows
- **Security Tests**: Payment security and fraud prevention
- **Performance Tests**: High-volume payment processing

## Implementation Tasks
1. **Database Schema Setup**
   - Create payment-related tables and indexes
   - Implement RLS policies for data security
   - Set up webhook event tracking

2. **Payment Service Development**
   - Build PaymentService with all operations
   - Implement Stripe integration
   - Create webhook handling system

3. **Frontend Components**
   - Build payment recording interface
   - Create payment history and details views
   - Implement Stripe payment forms

4. **Gateway Integration**
   - Set up Stripe webhook endpoints
   - Implement payment gateway configuration
   - Build gateway management interface

5. **Automated Workflows**
   - Build payment reminder system
   - Implement automated reconciliation
   - Create payment analytics

6. **Advanced Features**
   - Build refund processing system
   - Implement multi-currency support
   - Create payment plan functionality

## Dependencies
- **Invoice Management**: Invoice data and status updates
- **Customer Management**: Customer payment information
- **Business Management**: Business payment settings
- **Email Service**: Payment notifications and reminders
- **External Services**: Stripe, currency exchange APIs

## Success Criteria
- ✅ Payments can be recorded and tracked accurately
- ✅ Stripe integration works seamlessly
- ✅ Webhook processing is reliable and secure
- ✅ Refund processing works correctly
- ✅ Payment reminders are sent automatically
- ✅ All payment calculations are accurate
- ✅ Payment security measures are properly implemented
- ✅ Multi-currency support works correctly
