# Customer Relationship Management

## Overview
This module manages all customer-related data and interactions within the multi-tenant business context. It provides comprehensive customer profiles, contact management, communication history, and customer analytics to help businesses maintain strong relationships with their clients.

## Core Functionalities
- Customer profile management with detailed information
- Multiple contact persons per customer
- Billing and shipping address management
- Customer communication history and notes
- Customer categorization and tagging
- Customer import/export capabilities
- Customer search and filtering
- Customer analytics and insights
- Customer portal access (optional)
- Integration with invoicing and payment systems

## Technical Specifications

### Database Schema
```sql
-- Main customers table
CREATE TABLE app.customers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    -- Basic information
    customer_type TEXT NOT NULL DEFAULT 'individual' CHECK (customer_type IN ('individual', 'business')),
    name TEXT NOT NULL,
    display_name TEXT,
    email TEXT,
    phone TEXT,
    website TEXT,
    
    -- Business-specific fields
    company_name TEXT,
    tax_id TEXT,
    registration_number TEXT,
    
    -- Financial information
    currency TEXT DEFAULT 'USD',
    payment_terms INTEGER DEFAULT 30, -- Days
    credit_limit NUMERIC(12, 2),
    
    -- Status and categorization
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'archived')),
    category TEXT,
    tags TEXT[],
    
    -- Metadata
    notes TEXT,
    internal_notes TEXT, -- Private notes not visible to customer
    
    -- Tracking
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, email) WHERE email IS NOT NULL
);

-- Customer addresses (billing, shipping, etc.)
CREATE TABLE app.customer_addresses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES app.customers(id) ON DELETE CASCADE,
    
    address_type TEXT NOT NULL CHECK (address_type IN ('billing', 'shipping', 'mailing', 'other')),
    is_default BOOLEAN DEFAULT FALSE,
    
    -- Address fields
    label TEXT, -- e.g., "Main Office", "Warehouse"
    street_line_1 TEXT NOT NULL,
    street_line_2 TEXT,
    city TEXT NOT NULL,
    state_province TEXT,
    postal_code TEXT,
    country TEXT NOT NULL,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Customer contacts (multiple contacts per customer)
CREATE TABLE app.customer_contacts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES app.customers(id) ON DELETE CASCADE,
    
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    title TEXT, -- Job title
    department TEXT,
    email TEXT,
    phone TEXT,
    mobile TEXT,
    
    is_primary BOOLEAN DEFAULT FALSE,
    is_billing_contact BOOLEAN DEFAULT FALSE,
    
    notes TEXT,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(customer_id, email) WHERE email IS NOT NULL
);

-- Customer communication history
CREATE TABLE app.customer_communications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES app.customers(id) ON DELETE CASCADE,
    contact_id UUID REFERENCES app.customer_contacts(id),
    
    communication_type TEXT NOT NULL CHECK (communication_type IN ('email', 'phone', 'meeting', 'note', 'other')),
    direction TEXT NOT NULL CHECK (direction IN ('inbound', 'outbound', 'internal')),
    subject TEXT,
    content TEXT NOT NULL,
    
    -- Metadata
    communication_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    follow_up_date TIMESTAMPTZ,
    is_important BOOLEAN DEFAULT FALSE,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Customer documents and attachments
CREATE TABLE app.customer_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES app.customers(id) ON DELETE CASCADE,
    
    document_name TEXT NOT NULL,
    document_type TEXT,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type TEXT NOT NULL,
    
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE, -- Visible to customer in portal
    
    uploaded_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Customer analytics and metrics
CREATE TABLE app.customer_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES app.customers(id) ON DELETE CASCADE,
    
    metric_date DATE NOT NULL,
    total_invoices INTEGER DEFAULT 0,
    total_revenue NUMERIC(12, 2) DEFAULT 0,
    paid_invoices INTEGER DEFAULT 0,
    overdue_invoices INTEGER DEFAULT 0,
    average_payment_days NUMERIC(5, 2),
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(customer_id, metric_date)
);

-- Indexes for performance
CREATE INDEX idx_customers_business_id ON app.customers(business_id);
CREATE INDEX idx_customers_email ON app.customers(email);
CREATE INDEX idx_customers_status ON app.customers(status);
CREATE INDEX idx_customers_category ON app.customers(category);
CREATE INDEX idx_customer_addresses_customer_id ON app.customer_addresses(customer_id);
CREATE INDEX idx_customer_contacts_customer_id ON app.customer_contacts(customer_id);
CREATE INDEX idx_customer_communications_customer_id ON app.customer_communications(customer_id);
CREATE INDEX idx_customer_communications_date ON app.customer_communications(communication_date);
CREATE INDEX idx_customer_documents_customer_id ON app.customer_documents(customer_id);
CREATE INDEX idx_customer_metrics_customer_date ON app.customer_metrics(customer_id, metric_date);

-- Enable RLS
ALTER TABLE app.customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.customer_addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.customer_contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.customer_communications ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.customer_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.customer_metrics ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Business members can manage customers"
ON app.customers FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);

CREATE POLICY "Customer data inherits customer access"
ON app.customer_addresses FOR ALL
USING (
    customer_id IN (
        SELECT id FROM app.customers 
        WHERE business_id IN (
            SELECT business_id FROM app.business_users 
            WHERE user_id = auth.uid() AND is_active = TRUE
        )
    )
);

-- Similar policies for other customer-related tables
CREATE POLICY "Customer contacts inherit customer access"
ON app.customer_contacts FOR ALL
USING (
    customer_id IN (
        SELECT id FROM app.customers 
        WHERE business_id IN (
            SELECT business_id FROM app.business_users 
            WHERE user_id = auth.uid() AND is_active = TRUE
        )
    )
);

CREATE POLICY "Customer communications inherit customer access"
ON app.customer_communications FOR ALL
USING (
    customer_id IN (
        SELECT id FROM app.customers 
        WHERE business_id IN (
            SELECT business_id FROM app.business_users 
            WHERE user_id = auth.uid() AND is_active = TRUE
        )
    )
);
```

### Customer Service
```typescript
// lib/customer.ts
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

export interface Customer {
  id: string;
  business_id: string;
  customer_type: 'individual' | 'business';
  name: string;
  display_name?: string;
  email?: string;
  phone?: string;
  website?: string;
  company_name?: string;
  tax_id?: string;
  registration_number?: string;
  currency: string;
  payment_terms: number;
  credit_limit?: number;
  status: 'active' | 'inactive' | 'archived';
  category?: string;
  tags?: string[];
  notes?: string;
  internal_notes?: string;
  created_at: string;
  updated_at: string;
  addresses?: CustomerAddress[];
  contacts?: CustomerContact[];
}

export interface CustomerAddress {
  id: string;
  customer_id: string;
  address_type: 'billing' | 'shipping' | 'mailing' | 'other';
  is_default: boolean;
  label?: string;
  street_line_1: string;
  street_line_2?: string;
  city: string;
  state_province?: string;
  postal_code?: string;
  country: string;
}

export interface CustomerContact {
  id: string;
  customer_id: string;
  first_name: string;
  last_name: string;
  title?: string;
  department?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  is_primary: boolean;
  is_billing_contact: boolean;
  notes?: string;
}

export interface CustomerCommunication {
  id: string;
  customer_id: string;
  contact_id?: string;
  communication_type: 'email' | 'phone' | 'meeting' | 'note' | 'other';
  direction: 'inbound' | 'outbound' | 'internal';
  subject?: string;
  content: string;
  communication_date: string;
  follow_up_date?: string;
  is_important: boolean;
  created_by: string;
  created_at: string;
}

export class CustomerService {
  private supabase = createClientComponentClient();

  async createCustomer(businessId: string, customerData: Partial<Customer>): Promise<Customer> {
    const { data, error } = await this.supabase
      .from('customers')
      .insert({
        business_id: businessId,
        ...customerData,
        created_by: (await this.supabase.auth.getUser()).data.user?.id,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async updateCustomer(customerId: string, updates: Partial<Customer>): Promise<Customer> {
    const { data, error } = await this.supabase
      .from('customers')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', customerId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getCustomer(customerId: string): Promise<Customer> {
    const { data, error } = await this.supabase
      .from('customers')
      .select(`
        *,
        addresses:customer_addresses(*),
        contacts:customer_contacts(*)
      `)
      .eq('id', customerId)
      .single();

    if (error) throw error;
    return data;
  }

  async getCustomers(
    businessId: string,
    filters?: {
      status?: string;
      category?: string;
      search?: string;
      tags?: string[];
    },
    pagination?: { page: number; limit: number }
  ): Promise<{ customers: Customer[]; total: number }> {
    let query = this.supabase
      .from('customers')
      .select(`
        *,
        addresses:customer_addresses(*),
        contacts:customer_contacts(*)
      `, { count: 'exact' })
      .eq('business_id', businessId);

    // Apply filters
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }
    if (filters?.category) {
      query = query.eq('category', filters.category);
    }
    if (filters?.search) {
      query = query.or(`name.ilike.%${filters.search}%,email.ilike.%${filters.search}%,company_name.ilike.%${filters.search}%`);
    }
    if (filters?.tags && filters.tags.length > 0) {
      query = query.overlaps('tags', filters.tags);
    }

    // Apply pagination
    if (pagination) {
      const offset = (pagination.page - 1) * pagination.limit;
      query = query.range(offset, offset + pagination.limit - 1);
    }

    query = query.order('name');

    const { data, error, count } = await query;
    if (error) throw error;

    return {
      customers: data || [],
      total: count || 0,
    };
  }

  async addCustomerAddress(customerId: string, addressData: Partial<CustomerAddress>): Promise<CustomerAddress> {
    // If this is set as default, unset other defaults
    if (addressData.is_default) {
      await this.supabase
        .from('customer_addresses')
        .update({ is_default: false })
        .eq('customer_id', customerId)
        .eq('address_type', addressData.address_type);
    }

    const { data, error } = await this.supabase
      .from('customer_addresses')
      .insert({
        customer_id: customerId,
        ...addressData,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async updateCustomerAddress(addressId: string, updates: Partial<CustomerAddress>): Promise<CustomerAddress> {
    const { data, error } = await this.supabase
      .from('customer_addresses')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', addressId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async addCustomerContact(customerId: string, contactData: Partial<CustomerContact>): Promise<CustomerContact> {
    // If this is set as primary, unset other primary contacts
    if (contactData.is_primary) {
      await this.supabase
        .from('customer_contacts')
        .update({ is_primary: false })
        .eq('customer_id', customerId);
    }

    const { data, error } = await this.supabase
      .from('customer_contacts')
      .insert({
        customer_id: customerId,
        ...contactData,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async addCommunication(communicationData: Partial<CustomerCommunication>): Promise<CustomerCommunication> {
    const { data, error } = await this.supabase
      .from('customer_communications')
      .insert({
        ...communicationData,
        created_by: (await this.supabase.auth.getUser()).data.user?.id,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getCommunications(customerId: string, limit = 50): Promise<CustomerCommunication[]> {
    const { data, error } = await this.supabase
      .from('customer_communications')
      .select(`
        *,
        contact:customer_contacts(first_name, last_name),
        created_by_user:users(first_name, last_name)
      `)
      .eq('customer_id', customerId)
      .order('communication_date', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  }

  async getCustomerAnalytics(customerId: string): Promise<{
    totalInvoices: number;
    totalRevenue: number;
    averageInvoiceValue: number;
    paymentRate: number;
    averagePaymentDays: number;
    lastInvoiceDate?: string;
    lastPaymentDate?: string;
  }> {
    const { data, error } = await this.supabase.rpc('get_customer_analytics', {
      p_customer_id: customerId,
    });

    if (error) throw error;
    return data || {
      totalInvoices: 0,
      totalRevenue: 0,
      averageInvoiceValue: 0,
      paymentRate: 0,
      averagePaymentDays: 0,
    };
  }

  async importCustomers(businessId: string, customers: Partial<Customer>[]): Promise<{
    imported: number;
    errors: { row: number; error: string }[];
  }> {
    const results = {
      imported: 0,
      errors: [] as { row: number; error: string }[],
    };

    for (let i = 0; i < customers.length; i++) {
      try {
        await this.createCustomer(businessId, customers[i]);
        results.imported++;
      } catch (error) {
        results.errors.push({
          row: i + 1,
          error: error.message,
        });
      }
    }

    return results;
  }

  async exportCustomers(businessId: string, format: 'csv' | 'excel'): Promise<Blob> {
    const { customers } = await this.getCustomers(businessId);
    
    // This would typically call an Edge Function for export generation
    const response = await fetch('/api/customers/export', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        customers,
        format,
      }),
    });

    if (!response.ok) throw new Error('Export failed');
    return response.blob();
  }

  async searchCustomers(businessId: string, query: string): Promise<Customer[]> {
    const { data, error } = await this.supabase
      .from('customers')
      .select('*')
      .eq('business_id', businessId)
      .or(`name.ilike.%${query}%,email.ilike.%${query}%,company_name.ilike.%${query}%`)
      .limit(10);

    if (error) throw error;
    return data || [];
  }

  async getCustomerCategories(businessId: string): Promise<string[]> {
    const { data, error } = await this.supabase
      .from('customers')
      .select('category')
      .eq('business_id', businessId)
      .not('category', 'is', null);

    if (error) throw error;
    
    const categories = [...new Set(data.map(item => item.category).filter(Boolean))];
    return categories.sort();
  }

  async getCustomerTags(businessId: string): Promise<string[]> {
    const { data, error } = await this.supabase
      .from('customers')
      .select('tags')
      .eq('business_id', businessId)
      .not('tags', 'is', null);

    if (error) throw error;
    
    const allTags = data.flatMap(item => item.tags || []);
    const uniqueTags = [...new Set(allTags)];
    return uniqueTags.sort();
  }
}
```

## Security Measures
- **Business Isolation**: Complete customer data isolation between businesses
- **Contact Privacy**: Secure handling of personal contact information
- **Communication Security**: Encrypted storage of communication history
- **Document Security**: Secure file upload and access control
- **Data Validation**: Comprehensive input validation and sanitization
- **Access Control**: Role-based permissions for customer data

## Integration Points
- **Business Management**: Business-scoped customer data
- **Invoice Management**: Customer billing and invoice recipients
- **Payment Processing**: Customer payment information and history
- **User Management**: User activity tracking and permissions
- **Document Management**: Customer document storage and sharing

## Error Handling & Validation
- **Email Validation**: Valid email format and uniqueness
- **Phone Validation**: Phone number format validation
- **Address Validation**: Complete address information
- **Contact Validation**: Required contact information
- **Import Validation**: Bulk import error handling and reporting

## Testing Strategy
- **Unit Tests**: Customer service methods and validation
- **Integration Tests**: Database operations and RLS policies
- **E2E Tests**: Complete customer management workflows
- **Import/Export Tests**: Bulk operations and data integrity
- **Performance Tests**: Large customer dataset handling

## Implementation Tasks
1. **Database Schema Setup**
   - Create customer-related tables and indexes
   - Implement RLS policies for data isolation
   - Set up constraints and validation rules

2. **Customer Service Development**
   - Build CustomerService with all CRUD operations
   - Implement search and filtering capabilities
   - Create import/export functionality

3. **Frontend Components**
   - Build customer list and detail views
   - Create customer creation and editing forms
   - Implement contact and address management

4. **Communication System**
   - Build communication history tracking
   - Create communication entry interface
   - Implement follow-up reminders

5. **Analytics Integration**
   - Implement customer analytics calculations
   - Build customer insights dashboard
   - Create customer performance metrics

6. **Advanced Features**
   - Build customer portal access
   - Implement customer categorization
   - Create bulk operations interface

## Dependencies
- **Business Management**: Business context and user permissions
- **User Management**: User activity tracking and created_by fields
- **Document Management**: Customer document storage
- **Email Service**: Communication and notification delivery

## Success Criteria
- ✅ Customers can be created and managed completely
- ✅ Multiple addresses and contacts per customer work
- ✅ Communication history is tracked accurately
- ✅ Customer search and filtering is fast and accurate
- ✅ Import/export functionality works reliably
- ✅ Customer analytics provide valuable insights
- ✅ All customer data is properly isolated between businesses
