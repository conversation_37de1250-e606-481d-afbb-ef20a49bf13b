# Customer Relationship Management

## Overview
This module manages all customer-related data and interactions within the multi-tenant business context. It provides comprehensive customer profiles, contact management, communication history, and customer analytics to help businesses maintain strong relationships with their clients.

## Core Functionalities
- Customer profile management with detailed information
- Multiple contact persons per customer
- Billing and shipping address management
- Customer communication history and notes
- Customer categorization and tagging
- Customer import/export capabilities
- Customer search and filtering
- Customer analytics and insights
- Customer portal access (optional)
- Integration with invoicing and payment systems

## Technical Specifications

### Database Schema
```sql
-- Main customers table
CREATE TABLE app.customers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    -- Basic information
    customer_type TEXT NOT NULL DEFAULT 'individual' CHECK (customer_type IN ('individual', 'business')),
    name TEXT NOT NULL,
    display_name TEXT,
    email TEXT,
    phone TEXT,
    website TEXT,
    
    -- Business-specific fields
    company_name TEXT,
    tax_id TEXT,
    registration_number TEXT,
    
    -- Financial information
    currency TEXT DEFAULT 'USD',
    payment_terms INTEGER DEFAULT 30, -- Days
    credit_limit NUMERIC(12, 2),
    
    -- Status and categorization
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'archived')),
    category TEXT,
    tags TEXT[],
    
    -- Metadata
    notes TEXT,
    internal_notes TEXT, -- Private notes not visible to customer
    
    -- Tracking
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, email) WHERE email IS NOT NULL
);

-- Customer addresses (billing, shipping, etc.)
CREATE TABLE app.customer_addresses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES app.customers(id) ON DELETE CASCADE,
    
    address_type TEXT NOT NULL CHECK (address_type IN ('billing', 'shipping', 'mailing', 'other')),
    is_default BOOLEAN DEFAULT FALSE,
    
    -- Address fields
    label TEXT, -- e.g., "Main Office", "Warehouse"
    street_line_1 TEXT NOT NULL,
    street_line_2 TEXT,
    city TEXT NOT NULL,
    state_province TEXT,
    postal_code TEXT,
    country TEXT NOT NULL,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Customer contacts (multiple contacts per customer)
CREATE TABLE app.customer_contacts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES app.customers(id) ON DELETE CASCADE,
    
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    title TEXT, -- Job title
    department TEXT,
    email TEXT,
    phone TEXT,
    mobile TEXT,
    
    is_primary BOOLEAN DEFAULT FALSE,
    is_billing_contact BOOLEAN DEFAULT FALSE,
    
    notes TEXT,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(customer_id, email) WHERE email IS NOT NULL
);

-- Customer communication history
CREATE TABLE app.customer_communications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES app.customers(id) ON DELETE CASCADE,
    contact_id UUID REFERENCES app.customer_contacts(id),
    
    communication_type TEXT NOT NULL CHECK (communication_type IN ('email', 'phone', 'meeting', 'note', 'other')),
    direction TEXT NOT NULL CHECK (direction IN ('inbound', 'outbound', 'internal')),
    subject TEXT,
    content TEXT NOT NULL,
    
    -- Metadata
    communication_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    follow_up_date TIMESTAMPTZ,
    is_important BOOLEAN DEFAULT FALSE,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Customer documents and attachments
CREATE TABLE app.customer_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES app.customers(id) ON DELETE CASCADE,
    
    document_name TEXT NOT NULL,
    document_type TEXT,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type TEXT NOT NULL,
    
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE, -- Visible to customer in portal
    
    uploaded_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Customer analytics and metrics
CREATE TABLE app.customer_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES app.customers(id) ON DELETE CASCADE,
    
    metric_date DATE NOT NULL,
    total_invoices INTEGER DEFAULT 0,
    total_revenue NUMERIC(12, 2) DEFAULT 0,
    paid_invoices INTEGER DEFAULT 0,
    overdue_invoices INTEGER DEFAULT 0,
    average_payment_days NUMERIC(5, 2),
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(customer_id, metric_date)
);

-- Indexes for performance
CREATE INDEX idx_customers_business_id ON app.customers(business_id);
CREATE INDEX idx_customers_email ON app.customers(email);
CREATE INDEX idx_customers_status ON app.customers(status);
CREATE INDEX idx_customers_category ON app.customers(category);
CREATE INDEX idx_customer_addresses_customer_id ON app.customer_addresses(customer_id);
CREATE INDEX idx_customer_contacts_customer_id ON app.customer_contacts(customer_id);
CREATE INDEX idx_customer_communications_customer_id ON app.customer_communications(customer_id);
CREATE INDEX idx_customer_communications_date ON app.customer_communications(communication_date);
CREATE INDEX idx_customer_documents_customer_id ON app.customer_documents(customer_id);
CREATE INDEX idx_customer_metrics_customer_date ON app.customer_metrics(customer_id, metric_date);

-- Enable RLS
ALTER TABLE app.customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.customer_addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.customer_contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.customer_communications ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.customer_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.customer_metrics ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Business members can manage customers"
ON app.customers FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);

CREATE POLICY "Customer data inherits customer access"
ON app.customer_addresses FOR ALL
USING (
    customer_id IN (
        SELECT id FROM app.customers 
        WHERE business_id IN (
            SELECT business_id FROM app.business_users 
            WHERE user_id = auth.uid() AND is_active = TRUE
        )
    )
);

-- Similar policies for other customer-related tables
CREATE POLICY "Customer contacts inherit customer access"
ON app.customer_contacts FOR ALL
USING (
    customer_id IN (
        SELECT id FROM app.customers 
        WHERE business_id IN (
            SELECT business_id FROM app.business_users 
            WHERE user_id = auth.uid() AND is_active = TRUE
        )
    )
);

CREATE POLICY "Customer communications inherit customer access"
ON app.customer_communications FOR ALL
USING (
    customer_id IN (
        SELECT id FROM app.customers 
        WHERE business_id IN (
            SELECT business_id FROM app.business_users 
            WHERE user_id = auth.uid() AND is_active = TRUE
        )
    )
);
```

### Customer Service Architecture

**Core Service: CustomerService**
- **Primary Responsibilities**: Customer relationship management, contact tracking, communication history
- **Key Methods**:
  - `createCustomer(businessId, customerData)` - Customer registration and profile creation
  - `updateCustomer(customerId, updates)` - Customer information management
  - `getCustomer(customerId)` - Comprehensive customer data retrieval
  - `getCustomers(businessId, filters, pagination)` - Customer listing with advanced filtering
  - `addCustomerAddress(customerId, addressData)` - Multiple address management
  - `addCustomerContact(customerId, contactData)` - Contact person management
  - `addCommunication(communicationData)` - Communication history tracking
  - `getCommunications(customerId, limit)` - Communication history retrieval
  - `getCustomerAnalytics(customerId)` - Customer performance metrics
  - `importCustomers(businessId, customers)` - Bulk customer import with validation
  - `exportCustomers(businessId, format)` - Customer data export functionality
  - `searchCustomers(businessId, query)` - Advanced customer search

**Implementation Approach**:
- Build comprehensive customer profile system supporting both individual and business customers
- Implement multiple address management (billing, shipping, mailing)
- Create contact person hierarchy with primary and billing contact designation
- Establish communication tracking system with categorization and follow-up management
- Build customer analytics for business intelligence and relationship insights

### Customer Data Management Strategy

**Customer Profile System**:
- **Dual Customer Types**: Support for individual customers and business entities
- **Flexible Information**: Comprehensive profile fields with optional business-specific data
- **Address Management**: Multiple addresses per customer with type designation and defaults
- **Contact Hierarchy**: Multiple contact persons with role-based access and responsibilities
- **Categorization**: Customer categories and tags for organization and segmentation

**Communication Tracking**:
- **Multi-Channel History**: Email, phone, meeting, and note tracking
- **Direction Tracking**: Inbound, outbound, and internal communication classification
- **Follow-up Management**: Scheduled follow-ups and reminder system
- **Importance Flagging**: Priority communication identification
- **Context Preservation**: Rich communication context with attachments and metadata

**Customer Analytics**:
- **Financial Metrics**: Revenue tracking, payment behavior, and credit management
- **Relationship Insights**: Communication frequency, engagement levels, and satisfaction
- **Performance Tracking**: Invoice history, payment patterns, and business value
- **Segmentation Data**: Customer categorization for targeted marketing and service

## Security Measures
- **Business Isolation**: Complete customer data isolation between businesses
- **Contact Privacy**: Secure handling of personal contact information
- **Communication Security**: Encrypted storage of communication history
- **Document Security**: Secure file upload and access control
- **Data Validation**: Comprehensive input validation and sanitization
- **Access Control**: Role-based permissions for customer data

## Integration Points
- **Business Management**: Business-scoped customer data
- **Invoice Management**: Customer billing and invoice recipients
- **Payment Processing**: Customer payment information and history
- **User Management**: User activity tracking and permissions
- **Document Management**: Customer document storage and sharing

## Error Handling & Validation
- **Email Validation**: Valid email format and uniqueness
- **Phone Validation**: Phone number format validation
- **Address Validation**: Complete address information
- **Contact Validation**: Required contact information
- **Import Validation**: Bulk import error handling and reporting

## Testing Strategy
- **Unit Tests**: Customer service methods and validation
- **Integration Tests**: Database operations and RLS policies
- **E2E Tests**: Complete customer management workflows
- **Import/Export Tests**: Bulk operations and data integrity
- **Performance Tests**: Large customer dataset handling

## Implementation Tasks
1. **Database Schema Setup**
   - Create customer-related tables and indexes
   - Implement RLS policies for data isolation
   - Set up constraints and validation rules

2. **Customer Service Development**
   - Build CustomerService with all CRUD operations
   - Implement search and filtering capabilities
   - Create import/export functionality

3. **Frontend Components**
   - Build customer list and detail views
   - Create customer creation and editing forms
   - Implement contact and address management

4. **Communication System**
   - Build communication history tracking
   - Create communication entry interface
   - Implement follow-up reminders

5. **Analytics Integration**
   - Implement customer analytics calculations
   - Build customer insights dashboard
   - Create customer performance metrics

6. **Advanced Features**
   - Build customer portal access
   - Implement customer categorization
   - Create bulk operations interface

## Dependencies
- **Business Management**: Business context and user permissions
- **User Management**: User activity tracking and created_by fields
- **Document Management**: Customer document storage
- **Email Service**: Communication and notification delivery

## Success Criteria
- ✅ Customers can be created and managed completely
- ✅ Multiple addresses and contacts per customer work
- ✅ Communication history is tracked accurately
- ✅ Customer search and filtering is fast and accurate
- ✅ Import/export functionality works reliably
- ✅ Customer analytics provide valuable insights
- ✅ All customer data is properly isolated between businesses
