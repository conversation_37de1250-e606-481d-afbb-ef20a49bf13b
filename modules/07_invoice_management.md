# Invoice Management System

## Overview
This is the core module of rInvoice, handling the complete invoice lifecycle from creation to payment. It provides comprehensive invoice management with line items, tax calculations, discounts, recurring invoices, and automated workflows. The system ensures data integrity and provides immutable invoice records for compliance.

## Core Functionalities
- Invoice creation with dynamic line items
- Automatic invoice numbering and sequencing
- Tax calculation and multi-tax support
- Discount management (line-item and invoice-level)
- Invoice status tracking and workflow management
- Recurring invoice scheduling and automation
- Invoice templates and customization
- PDF generation and email delivery
- Invoice history and audit trail
- Bulk invoice operations

## Technical Specifications

### Database Schema
```sql
-- Invoice status enum
CREATE TYPE app.invoice_status AS ENUM (
    'draft', 'pending', 'sent', 'viewed', 'partial', 'paid', 'overdue', 'void', 'cancelled'
);

-- Recurring frequency enum
CREATE TYPE app.recurring_frequency AS ENUM (
    'weekly', 'monthly', 'quarterly', 'semi_annually', 'annually'
);

-- Main invoices table
CREATE TABLE app.invoices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES app.customers(id) ON DELETE RESTRICT,
    invoice_number TEXT NOT NULL,
    invoice_date DATE NOT NULL DEFAULT CURRENT_DATE,
    due_date DATE NOT NULL,
    status app.invoice_status NOT NULL DEFAULT 'draft',
    currency TEXT NOT NULL DEFAULT 'USD',
    exchange_rate NUMERIC(10, 6) DEFAULT 1.0,
    
    -- Amounts
    subtotal NUMERIC(12, 2) NOT NULL DEFAULT 0,
    tax_amount NUMERIC(12, 2) NOT NULL DEFAULT 0,
    discount_amount NUMERIC(12, 2) NOT NULL DEFAULT 0,
    total_amount NUMERIC(12, 2) NOT NULL DEFAULT 0,
    paid_amount NUMERIC(12, 2) NOT NULL DEFAULT 0,
    balance_due NUMERIC(12, 2) NOT NULL DEFAULT 0,
    
    -- Content
    notes TEXT,
    terms TEXT,
    footer TEXT,
    
    -- Metadata
    template_id UUID REFERENCES app.invoice_templates(id),
    parent_invoice_id UUID REFERENCES app.invoices(id), -- For recurring invoices
    is_recurring BOOLEAN DEFAULT FALSE,
    recurring_frequency app.recurring_frequency,
    recurring_end_date DATE,
    next_recurring_date DATE,
    
    -- Tracking
    sent_at TIMESTAMPTZ,
    viewed_at TIMESTAMPTZ,
    first_viewed_at TIMESTAMPTZ,
    view_count INTEGER DEFAULT 0,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, invoice_number),
    CHECK(due_date >= invoice_date),
    CHECK(total_amount >= 0),
    CHECK(paid_amount >= 0),
    CHECK(balance_due >= 0)
);

-- Invoice line items
CREATE TABLE app.invoice_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID NOT NULL REFERENCES app.invoices(id) ON DELETE CASCADE,
    product_id UUID REFERENCES app.products(id),
    
    -- Item details
    description TEXT NOT NULL,
    quantity NUMERIC(10, 3) NOT NULL CHECK (quantity > 0),
    unit_price NUMERIC(12, 2) NOT NULL CHECK (unit_price >= 0),
    
    -- Discounts
    discount_type TEXT CHECK (discount_type IN ('percentage', 'fixed')),
    discount_value NUMERIC(12, 2) DEFAULT 0,
    discount_amount NUMERIC(12, 2) DEFAULT 0,
    
    -- Tax
    tax_rate NUMERIC(5, 4) DEFAULT 0,
    tax_amount NUMERIC(12, 2) DEFAULT 0,
    
    -- Calculated amounts
    line_total NUMERIC(12, 2) NOT NULL DEFAULT 0,
    
    -- Ordering
    sort_order INTEGER NOT NULL DEFAULT 0,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Invoice templates for customization
CREATE TABLE app.invoice_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    
    -- Template configuration
    template_data JSONB NOT NULL,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, name)
);

-- Invoice history for audit trail
CREATE TABLE app.invoice_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID NOT NULL REFERENCES app.invoices(id) ON DELETE CASCADE,
    action TEXT NOT NULL,
    old_values JSONB,
    new_values JSONB,
    notes TEXT,
    
    created_by UUID REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Invoice attachments
CREATE TABLE app.invoice_attachments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID NOT NULL REFERENCES app.invoices(id) ON DELETE CASCADE,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type TEXT NOT NULL,
    
    uploaded_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_invoices_business_id ON app.invoices(business_id);
CREATE INDEX idx_invoices_customer_id ON app.invoices(customer_id);
CREATE INDEX idx_invoices_status ON app.invoices(status);
CREATE INDEX idx_invoices_due_date ON app.invoices(due_date);
CREATE INDEX idx_invoices_recurring ON app.invoices(is_recurring, next_recurring_date) WHERE is_recurring = TRUE;
CREATE INDEX idx_invoice_items_invoice_id ON app.invoice_items(invoice_id);
CREATE INDEX idx_invoice_history_invoice_id ON app.invoice_history(invoice_id);

-- Enable RLS
ALTER TABLE app.invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.invoice_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.invoice_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.invoice_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.invoice_attachments ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Business members can manage invoices"
ON app.invoices FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);

CREATE POLICY "Invoice items inherit invoice access"
ON app.invoice_items FOR ALL
USING (
    invoice_id IN (
        SELECT id FROM app.invoices 
        WHERE business_id IN (
            SELECT business_id FROM app.business_users 
            WHERE user_id = auth.uid() AND is_active = TRUE
        )
    )
);
```

### Invoice Service Architecture

**Core Service: InvoiceService**
- **Primary Responsibilities**: Invoice lifecycle management, line item handling, financial calculations, status tracking
- **Key Methods**:
  - `createInvoice(businessId, invoiceData)` - Invoice creation with automatic numbering
  - `updateInvoice(invoiceId, updates)` - Invoice modification and status management
  - `getInvoice(invoiceId)` - Comprehensive invoice retrieval with relations
  - `getInvoices(businessId, filters, pagination)` - Advanced invoice listing and filtering
  - `addLineItem(invoiceId, lineItemData)` - Line item management with automatic calculations
  - `updateLineItem(lineItemId, updates)` - Line item modification with total recalculation
  - `removeLineItem(lineItemId)` - Line item removal with invoice total updates
  - `sendInvoice(invoiceId, options)` - Invoice delivery via email with tracking
  - `markAsPaid(invoiceId, paymentAmount)` - Payment recording and status updates
  - `duplicateInvoice(invoiceId)` - Invoice duplication for recurring billing
  - `getInvoiceAnalytics(businessId, dateRange)` - Financial analytics and reporting

**Implementation Approach**:
- Build comprehensive invoice lifecycle from draft to payment
- Implement automatic financial calculations with tax and discount support
- Create flexible line item system supporting products and custom items
- Establish invoice numbering system with business-specific sequences
- Build invoice delivery system with email tracking and customer portal access

### Invoice Management Strategy

**Invoice Lifecycle**:
- **Status Progression**: Draft → Sent → Viewed → Partial/Paid → Overdue (if applicable)
- **Automatic Transitions**: Status updates based on payment activity and due dates
- **Manual Controls**: Override capabilities for special circumstances
- **Audit Trail**: Complete history of status changes and modifications

**Financial Calculations**:
- **Line Item Totals**: Quantity × Unit Price with discount and tax calculations
- **Invoice Totals**: Subtotal, tax amount, discount amount, and final total
- **Multi-Currency**: Support for different currencies with exchange rate tracking
- **Tax Integration**: Automatic tax calculation based on business rules and jurisdictions

**Document Generation**:
- **PDF Creation**: Professional invoice PDFs with customizable templates
- **Template System**: Multiple template options with business branding
- **Delivery Options**: Email delivery, customer portal access, and direct download
- **Tracking**: Email open tracking and customer engagement analytics



## Security Measures
- **Row-Level Security**: Complete business-scoped access control
- **Invoice Immutability**: History tracking for all changes
- **Number Uniqueness**: Unique invoice numbers per business
- **Amount Validation**: Comprehensive financial data validation
- **Status Workflow**: Controlled invoice status transitions
- **Audit Trail**: Complete history of all invoice operations

## Integration Points
- **Business Management**: Business-scoped invoice data
- **Customer Management**: Invoice recipients and billing information
- **Product Catalog**: Line item product references
- **Payment Processing**: Payment tracking and reconciliation
- **Document Management**: PDF generation and storage
- **Tax Management**: Tax calculation and compliance

## Error Handling & Validation
- **Financial Validation**: Amount calculations and constraints
- **Date Validation**: Due date after invoice date
- **Status Validation**: Valid status transitions only
- **Item Validation**: Positive quantities and valid prices
- **Currency Validation**: Supported currency codes
- **Business Rules**: Configurable validation rules

## Testing Strategy
- **Unit Tests**: Invoice calculations and business logic
- **Integration Tests**: Database operations and RLS policies
- **E2E Tests**: Complete invoice workflows
- **Financial Tests**: Accurate calculation testing
- **Performance Tests**: Large invoice handling
- **Security Tests**: Access control and data isolation

## Implementation Tasks
1. **Database Schema Implementation**
   - Create all invoice-related tables
   - Implement RLS policies and constraints
   - Set up indexes for performance

2. **Core Invoice Service**
   - Build InvoiceService with all CRUD operations
   - Implement calculation engine
   - Create invoice numbering system

3. **Frontend Components**
   - Build invoice creation and editing forms
   - Create invoice list and detail views
   - Implement invoice status management

4. **PDF Generation**
   - Set up PDF generation service
   - Create customizable invoice templates
   - Implement email delivery system

5. **Recurring Invoices**
   - Build recurring invoice scheduling
   - Implement automated generation
   - Create management interface

6. **Advanced Features**
   - Implement bulk operations
   - Build invoice analytics
   - Create advanced filtering and search

## Dependencies
- **Business Management**: Business context and user permissions
- **Customer Management**: Customer data and billing information
- **Product Catalog**: Product references and pricing
- **Document Management**: PDF templates and generation
- **Email Service**: Invoice delivery system

## Success Criteria
- ✅ Invoices can be created with accurate calculations
- ✅ Invoice numbering is unique and sequential
- ✅ PDF generation and email delivery works
- ✅ Invoice status workflow is properly enforced
- ✅ Recurring invoices are generated automatically
- ✅ All financial calculations are accurate
- ✅ Complete audit trail is maintained
- ✅ Performance is acceptable for large invoice volumes
