# Invoice Management System

## Overview
This is the core module of rInvoice, handling the complete invoice lifecycle from creation to payment. It provides comprehensive invoice management with line items, tax calculations, discounts, recurring invoices, and automated workflows. The system ensures data integrity and provides immutable invoice records for compliance.

## Core Functionalities
- Invoice creation with dynamic line items
- Automatic invoice numbering and sequencing
- Tax calculation and multi-tax support
- Discount management (line-item and invoice-level)
- Invoice status tracking and workflow management
- Recurring invoice scheduling and automation
- Invoice templates and customization
- PDF generation and email delivery
- Invoice history and audit trail
- Bulk invoice operations

## Technical Specifications

### Database Schema
```sql
-- Invoice status enum
CREATE TYPE app.invoice_status AS ENUM (
    'draft', 'pending', 'sent', 'viewed', 'partial', 'paid', 'overdue', 'void', 'cancelled'
);

-- Recurring frequency enum
CREATE TYPE app.recurring_frequency AS ENUM (
    'weekly', 'monthly', 'quarterly', 'semi_annually', 'annually'
);

-- Main invoices table
CREATE TABLE app.invoices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES app.customers(id) ON DELETE RESTRICT,
    invoice_number TEXT NOT NULL,
    invoice_date DATE NOT NULL DEFAULT CURRENT_DATE,
    due_date DATE NOT NULL,
    status app.invoice_status NOT NULL DEFAULT 'draft',
    currency TEXT NOT NULL DEFAULT 'USD',
    exchange_rate NUMERIC(10, 6) DEFAULT 1.0,
    
    -- Amounts
    subtotal NUMERIC(12, 2) NOT NULL DEFAULT 0,
    tax_amount NUMERIC(12, 2) NOT NULL DEFAULT 0,
    discount_amount NUMERIC(12, 2) NOT NULL DEFAULT 0,
    total_amount NUMERIC(12, 2) NOT NULL DEFAULT 0,
    paid_amount NUMERIC(12, 2) NOT NULL DEFAULT 0,
    balance_due NUMERIC(12, 2) NOT NULL DEFAULT 0,
    
    -- Content
    notes TEXT,
    terms TEXT,
    footer TEXT,
    
    -- Metadata
    template_id UUID REFERENCES app.invoice_templates(id),
    parent_invoice_id UUID REFERENCES app.invoices(id), -- For recurring invoices
    is_recurring BOOLEAN DEFAULT FALSE,
    recurring_frequency app.recurring_frequency,
    recurring_end_date DATE,
    next_recurring_date DATE,
    
    -- Tracking
    sent_at TIMESTAMPTZ,
    viewed_at TIMESTAMPTZ,
    first_viewed_at TIMESTAMPTZ,
    view_count INTEGER DEFAULT 0,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, invoice_number),
    CHECK(due_date >= invoice_date),
    CHECK(total_amount >= 0),
    CHECK(paid_amount >= 0),
    CHECK(balance_due >= 0)
);

-- Invoice line items
CREATE TABLE app.invoice_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID NOT NULL REFERENCES app.invoices(id) ON DELETE CASCADE,
    product_id UUID REFERENCES app.products(id),
    
    -- Item details
    description TEXT NOT NULL,
    quantity NUMERIC(10, 3) NOT NULL CHECK (quantity > 0),
    unit_price NUMERIC(12, 2) NOT NULL CHECK (unit_price >= 0),
    
    -- Discounts
    discount_type TEXT CHECK (discount_type IN ('percentage', 'fixed')),
    discount_value NUMERIC(12, 2) DEFAULT 0,
    discount_amount NUMERIC(12, 2) DEFAULT 0,
    
    -- Tax
    tax_rate NUMERIC(5, 4) DEFAULT 0,
    tax_amount NUMERIC(12, 2) DEFAULT 0,
    
    -- Calculated amounts
    line_total NUMERIC(12, 2) NOT NULL DEFAULT 0,
    
    -- Ordering
    sort_order INTEGER NOT NULL DEFAULT 0,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Invoice templates for customization
CREATE TABLE app.invoice_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    
    -- Template configuration
    template_data JSONB NOT NULL,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, name)
);

-- Invoice history for audit trail
CREATE TABLE app.invoice_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID NOT NULL REFERENCES app.invoices(id) ON DELETE CASCADE,
    action TEXT NOT NULL,
    old_values JSONB,
    new_values JSONB,
    notes TEXT,
    
    created_by UUID REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Invoice attachments
CREATE TABLE app.invoice_attachments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID NOT NULL REFERENCES app.invoices(id) ON DELETE CASCADE,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type TEXT NOT NULL,
    
    uploaded_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_invoices_business_id ON app.invoices(business_id);
CREATE INDEX idx_invoices_customer_id ON app.invoices(customer_id);
CREATE INDEX idx_invoices_status ON app.invoices(status);
CREATE INDEX idx_invoices_due_date ON app.invoices(due_date);
CREATE INDEX idx_invoices_recurring ON app.invoices(is_recurring, next_recurring_date) WHERE is_recurring = TRUE;
CREATE INDEX idx_invoice_items_invoice_id ON app.invoice_items(invoice_id);
CREATE INDEX idx_invoice_history_invoice_id ON app.invoice_history(invoice_id);

-- Enable RLS
ALTER TABLE app.invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.invoice_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.invoice_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.invoice_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.invoice_attachments ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Business members can manage invoices"
ON app.invoices FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);

CREATE POLICY "Invoice items inherit invoice access"
ON app.invoice_items FOR ALL
USING (
    invoice_id IN (
        SELECT id FROM app.invoices 
        WHERE business_id IN (
            SELECT business_id FROM app.business_users 
            WHERE user_id = auth.uid() AND is_active = TRUE
        )
    )
);
```

### Invoice Service
```typescript
// lib/invoice.ts
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

export interface Invoice {
  id: string;
  business_id: string;
  customer_id: string;
  invoice_number: string;
  invoice_date: string;
  due_date: string;
  status: 'draft' | 'pending' | 'sent' | 'viewed' | 'partial' | 'paid' | 'overdue' | 'void' | 'cancelled';
  currency: string;
  exchange_rate: number;
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  paid_amount: number;
  balance_due: number;
  notes?: string;
  terms?: string;
  footer?: string;
  is_recurring: boolean;
  recurring_frequency?: string;
  recurring_end_date?: string;
  next_recurring_date?: string;
  sent_at?: string;
  viewed_at?: string;
  view_count: number;
  created_at: string;
  updated_at: string;
  items: InvoiceItem[];
  customer?: any;
}

export interface InvoiceItem {
  id: string;
  invoice_id: string;
  product_id?: string;
  description: string;
  quantity: number;
  unit_price: number;
  discount_type?: 'percentage' | 'fixed';
  discount_value: number;
  discount_amount: number;
  tax_rate: number;
  tax_amount: number;
  line_total: number;
  sort_order: number;
}

export interface CreateInvoiceData {
  customer_id: string;
  invoice_date?: string;
  due_date: string;
  currency?: string;
  notes?: string;
  terms?: string;
  items: Omit<InvoiceItem, 'id' | 'invoice_id' | 'line_total' | 'tax_amount' | 'discount_amount'>[];
}

export class InvoiceService {
  private supabase = createClientComponentClient();

  async createInvoice(businessId: string, data: CreateInvoiceData): Promise<Invoice> {
    // Generate invoice number
    const invoiceNumber = await this.generateInvoiceNumber(businessId);
    
    // Calculate totals
    const calculatedItems = this.calculateItemTotals(data.items);
    const totals = this.calculateInvoiceTotals(calculatedItems);

    // Create invoice
    const { data: invoice, error: invoiceError } = await this.supabase
      .from('invoices')
      .insert({
        business_id: businessId,
        customer_id: data.customer_id,
        invoice_number: invoiceNumber,
        invoice_date: data.invoice_date || new Date().toISOString().split('T')[0],
        due_date: data.due_date,
        currency: data.currency || 'USD',
        notes: data.notes,
        terms: data.terms,
        subtotal: totals.subtotal,
        tax_amount: totals.tax_amount,
        discount_amount: totals.discount_amount,
        total_amount: totals.total_amount,
        balance_due: totals.total_amount,
        created_by: (await this.supabase.auth.getUser()).data.user?.id,
      })
      .select()
      .single();

    if (invoiceError) throw invoiceError;

    // Create invoice items
    const itemsWithInvoiceId = calculatedItems.map(item => ({
      ...item,
      invoice_id: invoice.id,
    }));

    const { error: itemsError } = await this.supabase
      .from('invoice_items')
      .insert(itemsWithInvoiceId);

    if (itemsError) throw itemsError;

    // Log creation
    await this.logInvoiceHistory(invoice.id, 'created', null, invoice);

    return this.getInvoice(invoice.id);
  }

  async updateInvoice(invoiceId: string, updates: Partial<Invoice>): Promise<Invoice> {
    const oldInvoice = await this.getInvoice(invoiceId);
    
    const { data, error } = await this.supabase
      .from('invoices')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', invoiceId)
      .select()
      .single();

    if (error) throw error;

    // Log update
    await this.logInvoiceHistory(invoiceId, 'updated', oldInvoice, data);

    return this.getInvoice(invoiceId);
  }

  async getInvoice(invoiceId: string): Promise<Invoice> {
    const { data, error } = await this.supabase
      .from('invoices')
      .select(`
        *,
        items:invoice_items(*),
        customer:customers(*)
      `)
      .eq('id', invoiceId)
      .single();

    if (error) throw error;
    return data;
  }

  async getInvoices(
    businessId: string,
    filters?: {
      status?: string;
      customer_id?: string;
      date_from?: string;
      date_to?: string;
      search?: string;
    },
    pagination?: { page: number; limit: number }
  ): Promise<{ invoices: Invoice[]; total: number }> {
    let query = this.supabase
      .from('invoices')
      .select(`
        *,
        customer:customers(name, email),
        items:invoice_items(*)
      `, { count: 'exact' })
      .eq('business_id', businessId);

    // Apply filters
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }
    if (filters?.customer_id) {
      query = query.eq('customer_id', filters.customer_id);
    }
    if (filters?.date_from) {
      query = query.gte('invoice_date', filters.date_from);
    }
    if (filters?.date_to) {
      query = query.lte('invoice_date', filters.date_to);
    }
    if (filters?.search) {
      query = query.or(`invoice_number.ilike.%${filters.search}%,notes.ilike.%${filters.search}%`);
    }

    // Apply pagination
    if (pagination) {
      const offset = (pagination.page - 1) * pagination.limit;
      query = query.range(offset, offset + pagination.limit - 1);
    }

    query = query.order('created_at', { ascending: false });

    const { data, error, count } = await query;
    if (error) throw error;

    return {
      invoices: data || [],
      total: count || 0,
    };
  }

  async sendInvoice(invoiceId: string, emailOptions?: {
    to?: string;
    subject?: string;
    message?: string;
  }): Promise<void> {
    // Update invoice status
    await this.updateInvoice(invoiceId, {
      status: 'sent',
      sent_at: new Date().toISOString(),
    });

    // Generate PDF and send email
    await this.generateAndSendInvoicePDF(invoiceId, emailOptions);

    // Log action
    await this.logInvoiceHistory(invoiceId, 'sent', null, { sent_at: new Date().toISOString() });
  }

  async markInvoiceAsPaid(invoiceId: string, paidAmount?: number): Promise<Invoice> {
    const invoice = await this.getInvoice(invoiceId);
    const amountPaid = paidAmount || invoice.total_amount;
    const newPaidAmount = invoice.paid_amount + amountPaid;
    const newBalanceDue = invoice.total_amount - newPaidAmount;
    
    const newStatus = newBalanceDue <= 0 ? 'paid' : 'partial';

    return this.updateInvoice(invoiceId, {
      status: newStatus,
      paid_amount: newPaidAmount,
      balance_due: newBalanceDue,
    });
  }

  async voidInvoice(invoiceId: string, reason?: string): Promise<Invoice> {
    const result = await this.updateInvoice(invoiceId, {
      status: 'void',
    });

    await this.logInvoiceHistory(invoiceId, 'voided', null, { reason });
    return result;
  }

  async duplicateInvoice(invoiceId: string): Promise<Invoice> {
    const originalInvoice = await this.getInvoice(invoiceId);
    
    const duplicateData: CreateInvoiceData = {
      customer_id: originalInvoice.customer_id,
      due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
      currency: originalInvoice.currency,
      notes: originalInvoice.notes,
      terms: originalInvoice.terms,
      items: originalInvoice.items.map(item => ({
        product_id: item.product_id,
        description: item.description,
        quantity: item.quantity,
        unit_price: item.unit_price,
        discount_type: item.discount_type,
        discount_value: item.discount_value,
        tax_rate: item.tax_rate,
        sort_order: item.sort_order,
      })),
    };

    return this.createInvoice(originalInvoice.business_id, duplicateData);
  }

  private async generateInvoiceNumber(businessId: string): Promise<string> {
    // Get the latest invoice number for this business
    const { data, error } = await this.supabase
      .from('invoices')
      .select('invoice_number')
      .eq('business_id', businessId)
      .order('created_at', { ascending: false })
      .limit(1);

    if (error) throw error;

    let nextNumber = 1;
    if (data && data.length > 0) {
      const lastNumber = data[0].invoice_number;
      const match = lastNumber.match(/(\d+)$/);
      if (match) {
        nextNumber = parseInt(match[1]) + 1;
      }
    }

    return `INV-${nextNumber.toString().padStart(6, '0')}`;
  }

  private calculateItemTotals(items: any[]): any[] {
    return items.map((item, index) => {
      const lineSubtotal = item.quantity * item.unit_price;
      
      // Calculate discount
      let discountAmount = 0;
      if (item.discount_type === 'percentage') {
        discountAmount = lineSubtotal * (item.discount_value / 100);
      } else if (item.discount_type === 'fixed') {
        discountAmount = item.discount_value;
      }

      const afterDiscount = lineSubtotal - discountAmount;
      const taxAmount = afterDiscount * (item.tax_rate / 100);
      const lineTotal = afterDiscount + taxAmount;

      return {
        ...item,
        discount_amount: discountAmount,
        tax_amount: taxAmount,
        line_total: lineTotal,
        sort_order: index,
      };
    });
  }

  private calculateInvoiceTotals(items: any[]) {
    const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0);
    const discount_amount = items.reduce((sum, item) => sum + item.discount_amount, 0);
    const tax_amount = items.reduce((sum, item) => sum + item.tax_amount, 0);
    const total_amount = subtotal - discount_amount + tax_amount;

    return {
      subtotal,
      discount_amount,
      tax_amount,
      total_amount,
    };
  }

  private async logInvoiceHistory(
    invoiceId: string,
    action: string,
    oldValues: any,
    newValues: any,
    notes?: string
  ): Promise<void> {
    const user = await this.supabase.auth.getUser();
    
    await this.supabase
      .from('invoice_history')
      .insert({
        invoice_id: invoiceId,
        action,
        old_values: oldValues,
        new_values: newValues,
        notes,
        created_by: user.data.user?.id,
      });
  }

  private async generateAndSendInvoicePDF(
    invoiceId: string,
    emailOptions?: any
  ): Promise<void> {
    // This would typically call a Supabase Edge Function
    // that generates the PDF and sends the email
    await fetch('/api/invoices/send', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        invoiceId,
        emailOptions,
      }),
    });
  }
}
```

## Security Measures
- **Row-Level Security**: Complete business-scoped access control
- **Invoice Immutability**: History tracking for all changes
- **Number Uniqueness**: Unique invoice numbers per business
- **Amount Validation**: Comprehensive financial data validation
- **Status Workflow**: Controlled invoice status transitions
- **Audit Trail**: Complete history of all invoice operations

## Integration Points
- **Business Management**: Business-scoped invoice data
- **Customer Management**: Invoice recipients and billing information
- **Product Catalog**: Line item product references
- **Payment Processing**: Payment tracking and reconciliation
- **Document Management**: PDF generation and storage
- **Tax Management**: Tax calculation and compliance

## Error Handling & Validation
- **Financial Validation**: Amount calculations and constraints
- **Date Validation**: Due date after invoice date
- **Status Validation**: Valid status transitions only
- **Item Validation**: Positive quantities and valid prices
- **Currency Validation**: Supported currency codes
- **Business Rules**: Configurable validation rules

## Testing Strategy
- **Unit Tests**: Invoice calculations and business logic
- **Integration Tests**: Database operations and RLS policies
- **E2E Tests**: Complete invoice workflows
- **Financial Tests**: Accurate calculation testing
- **Performance Tests**: Large invoice handling
- **Security Tests**: Access control and data isolation

## Implementation Tasks
1. **Database Schema Implementation**
   - Create all invoice-related tables
   - Implement RLS policies and constraints
   - Set up indexes for performance

2. **Core Invoice Service**
   - Build InvoiceService with all CRUD operations
   - Implement calculation engine
   - Create invoice numbering system

3. **Frontend Components**
   - Build invoice creation and editing forms
   - Create invoice list and detail views
   - Implement invoice status management

4. **PDF Generation**
   - Set up PDF generation service
   - Create customizable invoice templates
   - Implement email delivery system

5. **Recurring Invoices**
   - Build recurring invoice scheduling
   - Implement automated generation
   - Create management interface

6. **Advanced Features**
   - Implement bulk operations
   - Build invoice analytics
   - Create advanced filtering and search

## Dependencies
- **Business Management**: Business context and user permissions
- **Customer Management**: Customer data and billing information
- **Product Catalog**: Product references and pricing
- **Document Management**: PDF templates and generation
- **Email Service**: Invoice delivery system

## Success Criteria
- ✅ Invoices can be created with accurate calculations
- ✅ Invoice numbering is unique and sequential
- ✅ PDF generation and email delivery works
- ✅ Invoice status workflow is properly enforced
- ✅ Recurring invoices are generated automatically
- ✅ All financial calculations are accurate
- ✅ Complete audit trail is maintained
- ✅ Performance is acceptable for large invoice volumes
