# Core Infrastructure & Setup

## Overview
This foundational module establishes the complete technical infrastructure for rInvoice, including project setup, database architecture, development environment, and deployment pipeline. It serves as the backbone for all other modules and ensures scalability, security, and maintainability from day one.

## Core Functionalities
- Next.js 14 project setup with TypeScript and Tailwind CSS
- Supabase project configuration with PostgreSQL database
- Multi-tenant database schema with Row-Level Security (RLS)
- Development environment setup and tooling
- CI/CD pipeline configuration for Cloudflare Pages
- Error monitoring and logging infrastructure
- Performance monitoring and analytics setup

## Technical Specifications

### Project Structure
```
rInvoice/
├── src/
│   ├── app/                    # Next.js 14 App Router
│   ├── components/             # Reusable UI components
│   ├── lib/                    # Utilities and configurations
│   ├── hooks/                  # Custom React hooks
│   ├── types/                  # TypeScript type definitions
│   └── styles/                 # Global styles and Tailwind config
├── supabase/
│   ├── migrations/             # Database migrations
│   ├── functions/              # Edge Functions
│   └── config.toml             # Supabase configuration
├── tests/
│   ├── unit/                   # Unit tests
│   ├── integration/            # Integration tests
│   └── e2e/                    # End-to-end tests
└── docs/                       # Documentation
```

### Database Foundation Schema
```sql
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create application schema
CREATE SCHEMA IF NOT EXISTS app;

-- Create enum types
CREATE TYPE app.user_role AS ENUM ('owner', 'admin', 'manager', 'user', 'viewer');
CREATE TYPE app.subscription_status AS ENUM ('trial', 'active', 'past_due', 'canceled', 'unpaid');
CREATE TYPE app.business_status AS ENUM ('active', 'suspended', 'deleted');

-- Core businesses table (multi-tenant foundation)
CREATE TABLE app.businesses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    email TEXT NOT NULL,
    phone TEXT,
    website TEXT,
    logo_url TEXT,
    address JSONB,
    tax_id TEXT,
    currency TEXT NOT NULL DEFAULT 'USD',
    timezone TEXT NOT NULL DEFAULT 'UTC',
    status app.business_status NOT NULL DEFAULT 'active',
    subscription_status app.subscription_status NOT NULL DEFAULT 'trial',
    subscription_plan TEXT,
    trial_ends_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Users table (extends Supabase auth.users)
CREATE TABLE app.users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    avatar_url TEXT,
    phone TEXT,
    timezone TEXT DEFAULT 'UTC',
    language TEXT DEFAULT 'en',
    email_verified BOOLEAN DEFAULT FALSE,
    last_login_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Business-User relationship (many-to-many with roles)
CREATE TABLE app.business_users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES app.users(id) ON DELETE CASCADE,
    role app.user_role NOT NULL DEFAULT 'user',
    invited_by UUID REFERENCES app.users(id),
    invited_at TIMESTAMPTZ,
    joined_at TIMESTAMPTZ,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(business_id, user_id)
);

-- Indexes for performance
CREATE INDEX idx_businesses_slug ON app.businesses(slug);
CREATE INDEX idx_businesses_status ON app.businesses(status);
CREATE INDEX idx_users_email ON app.users(email);
CREATE INDEX idx_business_users_business_id ON app.business_users(business_id);
CREATE INDEX idx_business_users_user_id ON app.business_users(user_id);

-- Enable RLS on all tables
ALTER TABLE app.businesses ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.business_users ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own profile"
ON app.users FOR ALL
USING (auth.uid() = id);

CREATE POLICY "Business members can view business details"
ON app.businesses FOR ALL
USING (
    id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() AND is_active = TRUE
    )
);

CREATE POLICY "Users can view their business relationships"
ON app.business_users FOR ALL
USING (user_id = auth.uid());
```

### Environment Configuration
```typescript
// lib/config.ts
export const config = {
  supabase: {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY!,
  },
  app: {
    name: 'rInvoice',
    url: process.env.NEXT_PUBLIC_APP_URL!,
    environment: process.env.NODE_ENV,
  },
  monitoring: {
    sentryDsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
    posthogKey: process.env.NEXT_PUBLIC_POSTHOG_KEY,
  },
  email: {
    resendApiKey: process.env.RESEND_API_KEY,
    fromEmail: process.env.FROM_EMAIL || '<EMAIL>',
  },
  stripe: {
    publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!,
    secretKey: process.env.STRIPE_SECRET_KEY!,
    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET!,
  },
};
```

### API Response Standards
```typescript
// types/api.ts
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

export interface ApiError {
  code: string;
  message: string;
  statusCode: number;
  details?: any;
}
```

## Security Measures
- **Row-Level Security (RLS)**: All tables have comprehensive RLS policies
- **Environment Variables**: Secure configuration management
- **HTTPS Enforcement**: SSL/TLS for all communications
- **CORS Configuration**: Proper cross-origin resource sharing setup
- **Rate Limiting**: API endpoint protection against abuse
- **Input Validation**: Zod schemas for all API inputs
- **SQL Injection Prevention**: Parameterized queries only
- **XSS Protection**: Content Security Policy headers

## Integration Points
- **Supabase Auth**: User authentication and session management
- **Cloudflare Pages**: Frontend hosting and CDN
- **Sentry**: Error monitoring and performance tracking
- **PostHog**: User analytics and feature flags
- **Resend/SendGrid**: Email delivery service
- **Stripe**: Payment processing integration

## Error Handling & Validation
- **Global Error Boundary**: React error boundary for UI errors
- **API Error Handling**: Standardized error responses
- **Validation Schemas**: Zod for runtime type checking
- **Database Constraints**: Foreign keys and check constraints
- **Transaction Management**: Atomic operations for data consistency
- **Retry Logic**: Automatic retry for transient failures

## Testing Strategy
- **Unit Tests**: Jest/Vitest for utility functions and components
- **Integration Tests**: Supabase client and API endpoint testing
- **E2E Tests**: Playwright for complete user workflows
- **Database Tests**: Migration and RLS policy testing
- **Performance Tests**: Load testing with k6
- **Security Tests**: OWASP ZAP automated security scanning

## Implementation Tasks
1. **Project Setup**
   - Initialize Next.js 14 project with TypeScript
   - Configure Tailwind CSS and component library
   - Set up ESLint, Prettier, and Husky pre-commit hooks
   - Configure absolute imports and path mapping

2. **Supabase Configuration**
   - Create Supabase project and configure environment
   - Set up local development with Supabase CLI
   - Create initial database schema and migrations
   - Configure RLS policies and test access patterns

3. **Development Environment**
   - Set up Docker for consistent development environment
   - Configure VS Code workspace and recommended extensions
   - Set up environment variable management
   - Create development scripts and documentation

4. **CI/CD Pipeline**
   - Configure GitHub Actions for automated testing
   - Set up Cloudflare Pages deployment
   - Configure staging and production environments
   - Set up database migration automation

5. **Monitoring & Logging**
   - Integrate Sentry for error tracking
   - Set up PostHog for analytics
   - Configure performance monitoring
   - Set up log aggregation and alerting

6. **Security Implementation**
   - Configure Content Security Policy
   - Set up rate limiting middleware
   - Implement API authentication middleware
   - Configure CORS and security headers

## Dependencies
- **External Services**: Supabase, Cloudflare Pages, Sentry, PostHog
- **Development Tools**: Node.js 18+, Git, Docker
- **Package Dependencies**: Next.js, React, TypeScript, Tailwind CSS
- **Database**: PostgreSQL 15+ (via Supabase)

## Success Criteria
- ✅ Project builds and deploys successfully
- ✅ Database migrations run without errors
- ✅ RLS policies prevent unauthorized access
- ✅ All tests pass in CI/CD pipeline
- ✅ Error monitoring captures and reports issues
- ✅ Performance metrics meet baseline requirements
- ✅ Security headers and policies are properly configured
