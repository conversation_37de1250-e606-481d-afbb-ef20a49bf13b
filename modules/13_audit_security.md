# Audit Logging & Security

## Overview
This module implements comprehensive audit logging, security monitoring, and compliance features to ensure data integrity, track all user actions, and maintain regulatory compliance. It provides detailed audit trails, security event monitoring, and automated threat detection capabilities.

## Core Functionalities
- Comprehensive audit logging for all business operations
- Security event monitoring and alerting
- User activity tracking and session management
- Data change tracking with before/after values
- Compliance reporting and data retention policies
- Automated threat detection and response
- Security dashboard and analytics
- Data export for compliance audits
- Role-based audit log access
- Real-time security monitoring

## Technical Specifications

### Database Schema
```sql
-- Audit log levels
CREATE TYPE app.audit_level AS ENUM ('info', 'warning', 'error', 'critical');

-- Audit event types
CREATE TYPE app.audit_event_type AS ENUM (
    'user_login', 'user_logout', 'user_created', 'user_updated', 'user_deleted',
    'business_created', 'business_updated', 'business_deleted',
    'customer_created', 'customer_updated', 'customer_deleted',
    'product_created', 'product_updated', 'product_deleted',
    'invoice_created', 'invoice_updated', 'invoice_sent', 'invoice_paid', 'invoice_voided',
    'payment_created', 'payment_updated', 'payment_refunded',
    'settings_updated', 'permission_changed', 'data_exported',
    'security_violation', 'failed_login', 'suspicious_activity'
);

-- Main audit logs table
CREATE TABLE app.audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID REFERENCES app.businesses(id) ON DELETE CASCADE,
    user_id UUID REFERENCES app.users(id) ON DELETE SET NULL,
    
    -- Event details
    event_type app.audit_event_type NOT NULL,
    event_level app.audit_level NOT NULL DEFAULT 'info',
    event_description TEXT NOT NULL,
    
    -- Resource information
    resource_type TEXT, -- 'invoice', 'customer', 'product', etc.
    resource_id UUID,
    resource_name TEXT,
    
    -- Change tracking
    old_values JSONB,
    new_values JSONB,
    changed_fields TEXT[],
    
    -- Request context
    ip_address INET,
    user_agent TEXT,
    request_id TEXT,
    session_id TEXT,
    
    -- Additional metadata
    metadata JSONB,
    tags TEXT[],
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Partitioning helper
    created_date DATE NOT NULL DEFAULT CURRENT_DATE
) PARTITION BY RANGE (created_date);

-- Create partitions for audit logs (monthly partitions)
CREATE TABLE app.audit_logs_2024_01 PARTITION OF app.audit_logs
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
CREATE TABLE app.audit_logs_2024_02 PARTITION OF app.audit_logs
    FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');
-- Additional partitions would be created automatically

-- Security events table for specific security monitoring
CREATE TABLE app.security_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID REFERENCES app.businesses(id) ON DELETE CASCADE,
    user_id UUID REFERENCES app.users(id) ON DELETE SET NULL,
    
    event_type TEXT NOT NULL,
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    
    -- Event details
    source_ip INET,
    user_agent TEXT,
    location JSONB, -- Geolocation data
    
    -- Detection details
    detection_method TEXT, -- 'rule', 'ml', 'manual'
    rule_id TEXT,
    confidence_score NUMERIC(3, 2), -- 0.00 to 1.00
    
    -- Response
    status TEXT NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'investigating', 'resolved', 'false_positive')),
    assigned_to UUID REFERENCES app.users(id),
    resolved_at TIMESTAMPTZ,
    resolution_notes TEXT,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Data retention policies
CREATE TABLE app.data_retention_policies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    data_type TEXT NOT NULL, -- 'audit_logs', 'user_sessions', etc.
    retention_period_days INTEGER NOT NULL,
    auto_delete BOOLEAN DEFAULT TRUE,
    
    -- Compliance requirements
    compliance_reason TEXT,
    legal_hold BOOLEAN DEFAULT FALSE,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(business_id, data_type)
);

-- Compliance reports
CREATE TABLE app.compliance_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    
    report_type TEXT NOT NULL, -- 'gdpr', 'sox', 'hipaa', etc.
    report_period_start DATE NOT NULL,
    report_period_end DATE NOT NULL,
    
    -- Report content
    report_data JSONB NOT NULL,
    file_path TEXT, -- Path to generated report file
    
    -- Status
    status TEXT NOT NULL DEFAULT 'generated' CHECK (status IN ('generated', 'reviewed', 'submitted')),
    reviewed_by UUID REFERENCES app.users(id),
    reviewed_at TIMESTAMPTZ,
    
    generated_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Security rules for automated threat detection
CREATE TABLE app.security_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID REFERENCES app.businesses(id) ON DELETE CASCADE, -- NULL for global rules
    
    rule_name TEXT NOT NULL,
    rule_description TEXT,
    rule_type TEXT NOT NULL CHECK (rule_type IN ('threshold', 'pattern', 'anomaly', 'geo')),
    
    -- Rule configuration
    rule_config JSONB NOT NULL,
    
    -- Thresholds and actions
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    action TEXT NOT NULL CHECK (action IN ('log', 'alert', 'block', 'require_mfa')),
    
    is_active BOOLEAN DEFAULT TRUE,
    
    created_by UUID NOT NULL REFERENCES app.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_audit_logs_business_id ON app.audit_logs(business_id);
CREATE INDEX idx_audit_logs_user_id ON app.audit_logs(user_id);
CREATE INDEX idx_audit_logs_event_type ON app.audit_logs(event_type);
CREATE INDEX idx_audit_logs_created_at ON app.audit_logs(created_at);
CREATE INDEX idx_audit_logs_resource ON app.audit_logs(resource_type, resource_id);
CREATE INDEX idx_security_events_business_id ON app.security_events(business_id);
CREATE INDEX idx_security_events_severity ON app.security_events(severity);
CREATE INDEX idx_security_events_status ON app.security_events(status);
CREATE INDEX idx_security_events_created_at ON app.security_events(created_at);
CREATE INDEX idx_compliance_reports_business_id ON app.compliance_reports(business_id);
CREATE INDEX idx_security_rules_business_id ON app.security_rules(business_id);

-- Enable RLS
ALTER TABLE app.audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.security_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.data_retention_policies ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.compliance_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.security_rules ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Business admins can view audit logs"
ON app.audit_logs FOR SELECT
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() 
        AND role IN ('owner', 'admin')
        AND is_active = TRUE
    )
    OR user_id = auth.uid() -- Users can see their own actions
);

CREATE POLICY "Business owners can manage security settings"
ON app.security_rules FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users 
        WHERE user_id = auth.uid() 
        AND role = 'owner'
        AND is_active = TRUE
    )
    OR business_id IS NULL -- Global rules managed by system admins
);
```

### Audit Service
```typescript
// lib/audit.ts
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

export interface AuditLog {
  id: string;
  business_id?: string;
  user_id?: string;
  event_type: string;
  event_level: 'info' | 'warning' | 'error' | 'critical';
  event_description: string;
  resource_type?: string;
  resource_id?: string;
  resource_name?: string;
  old_values?: any;
  new_values?: any;
  changed_fields?: string[];
  ip_address?: string;
  user_agent?: string;
  request_id?: string;
  session_id?: string;
  metadata?: any;
  tags?: string[];
  created_at: string;
}

export interface SecurityEvent {
  id: string;
  business_id?: string;
  user_id?: string;
  event_type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  source_ip?: string;
  user_agent?: string;
  location?: any;
  detection_method?: string;
  rule_id?: string;
  confidence_score?: number;
  status: 'open' | 'investigating' | 'resolved' | 'false_positive';
  assigned_to?: string;
  resolved_at?: string;
  resolution_notes?: string;
  created_at: string;
  updated_at: string;
}

export interface SecurityRule {
  id: string;
  business_id?: string;
  rule_name: string;
  rule_description?: string;
  rule_type: 'threshold' | 'pattern' | 'anomaly' | 'geo';
  rule_config: any;
  severity: 'low' | 'medium' | 'high' | 'critical';
  action: 'log' | 'alert' | 'block' | 'require_mfa';
  is_active: boolean;
}

export class AuditService {
  private supabase = createClientComponentClient();

  async logEvent(eventData: {
    business_id?: string;
    event_type: string;
    event_level?: 'info' | 'warning' | 'error' | 'critical';
    event_description: string;
    resource_type?: string;
    resource_id?: string;
    resource_name?: string;
    old_values?: any;
    new_values?: any;
    metadata?: any;
    tags?: string[];
  }): Promise<void> {
    try {
      const user = await this.supabase.auth.getUser();
      const requestContext = await this.getRequestContext();

      // Determine changed fields if old and new values are provided
      let changedFields: string[] | undefined;
      if (eventData.old_values && eventData.new_values) {
        changedFields = this.getChangedFields(eventData.old_values, eventData.new_values);
      }

      await this.supabase
        .from('audit_logs')
        .insert({
          business_id: eventData.business_id,
          user_id: user.data.user?.id,
          event_type: eventData.event_type,
          event_level: eventData.event_level || 'info',
          event_description: eventData.event_description,
          resource_type: eventData.resource_type,
          resource_id: eventData.resource_id,
          resource_name: eventData.resource_name,
          old_values: eventData.old_values,
          new_values: eventData.new_values,
          changed_fields: changedFields,
          ip_address: requestContext.ip_address,
          user_agent: requestContext.user_agent,
          request_id: requestContext.request_id,
          session_id: requestContext.session_id,
          metadata: eventData.metadata,
          tags: eventData.tags,
        });

      // Check security rules
      await this.checkSecurityRules(eventData, requestContext);
    } catch (error) {
      console.error('Failed to log audit event:', error);
      // Don't throw error to avoid breaking the main operation
    }
  }

  async getAuditLogs(
    businessId: string,
    filters?: {
      user_id?: string;
      event_type?: string;
      event_level?: string;
      resource_type?: string;
      date_from?: string;
      date_to?: string;
      search?: string;
    },
    pagination?: { page: number; limit: number }
  ): Promise<{ logs: AuditLog[]; total: number }> {
    let query = this.supabase
      .from('audit_logs')
      .select(`
        *,
        user:users(first_name, last_name, email)
      `, { count: 'exact' })
      .eq('business_id', businessId);

    // Apply filters
    if (filters?.user_id) {
      query = query.eq('user_id', filters.user_id);
    }
    if (filters?.event_type) {
      query = query.eq('event_type', filters.event_type);
    }
    if (filters?.event_level) {
      query = query.eq('event_level', filters.event_level);
    }
    if (filters?.resource_type) {
      query = query.eq('resource_type', filters.resource_type);
    }
    if (filters?.date_from) {
      query = query.gte('created_at', filters.date_from);
    }
    if (filters?.date_to) {
      query = query.lte('created_at', filters.date_to);
    }
    if (filters?.search) {
      query = query.or(`event_description.ilike.%${filters.search}%,resource_name.ilike.%${filters.search}%`);
    }

    // Apply pagination
    if (pagination) {
      const offset = (pagination.page - 1) * pagination.limit;
      query = query.range(offset, offset + pagination.limit - 1);
    }

    query = query.order('created_at', { ascending: false });

    const { data, error, count } = await query;
    if (error) throw error;

    return {
      logs: data || [],
      total: count || 0,
    };
  }

  async createSecurityEvent(eventData: {
    business_id?: string;
    event_type: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    title: string;
    description: string;
    detection_method?: string;
    rule_id?: string;
    confidence_score?: number;
    metadata?: any;
  }): Promise<SecurityEvent> {
    const user = await this.supabase.auth.getUser();
    const requestContext = await this.getRequestContext();

    const { data, error } = await this.supabase
      .from('security_events')
      .insert({
        business_id: eventData.business_id,
        user_id: user.data.user?.id,
        event_type: eventData.event_type,
        severity: eventData.severity,
        title: eventData.title,
        description: eventData.description,
        source_ip: requestContext.ip_address,
        user_agent: requestContext.user_agent,
        location: await this.getLocationFromIP(requestContext.ip_address),
        detection_method: eventData.detection_method || 'manual',
        rule_id: eventData.rule_id,
        confidence_score: eventData.confidence_score,
      })
      .select()
      .single();

    if (error) throw error;

    // Send alerts for high/critical severity events
    if (eventData.severity === 'high' || eventData.severity === 'critical') {
      await this.sendSecurityAlert(data);
    }

    return data;
  }

  async getSecurityEvents(
    businessId: string,
    filters?: {
      severity?: string;
      status?: string;
      event_type?: string;
      date_from?: string;
      date_to?: string;
    },
    pagination?: { page: number; limit: number }
  ): Promise<{ events: SecurityEvent[]; total: number }> {
    let query = this.supabase
      .from('security_events')
      .select('*', { count: 'exact' })
      .eq('business_id', businessId);

    // Apply filters
    if (filters?.severity) {
      query = query.eq('severity', filters.severity);
    }
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }
    if (filters?.event_type) {
      query = query.eq('event_type', filters.event_type);
    }
    if (filters?.date_from) {
      query = query.gte('created_at', filters.date_from);
    }
    if (filters?.date_to) {
      query = query.lte('created_at', filters.date_to);
    }

    // Apply pagination
    if (pagination) {
      const offset = (pagination.page - 1) * pagination.limit;
      query = query.range(offset, offset + pagination.limit - 1);
    }

    query = query.order('created_at', { ascending: false });

    const { data, error, count } = await query;
    if (error) throw error;

    return {
      events: data || [],
      total: count || 0,
    };
  }

  async generateComplianceReport(
    businessId: string,
    reportType: string,
    periodStart: string,
    periodEnd: string
  ): Promise<any> {
    // Generate compliance report based on audit logs
    const { logs } = await this.getAuditLogs(businessId, {
      date_from: periodStart,
      date_to: periodEnd,
    });

    const reportData = {
      business_id: businessId,
      report_type: reportType,
      period_start: periodStart,
      period_end: periodEnd,
      total_events: logs.length,
      events_by_type: this.groupEventsByType(logs),
      events_by_user: this.groupEventsByUser(logs),
      security_events: await this.getSecurityEventsSummary(businessId, periodStart, periodEnd),
      data_access_summary: this.getDataAccessSummary(logs),
      generated_at: new Date().toISOString(),
    };

    // Save report
    const { data, error } = await this.supabase
      .from('compliance_reports')
      .insert({
        business_id: businessId,
        report_type: reportType,
        report_period_start: periodStart,
        report_period_end: periodEnd,
        report_data: reportData,
        generated_by: (await this.supabase.auth.getUser()).data.user?.id,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async exportAuditLogs(
    businessId: string,
    format: 'csv' | 'json' | 'pdf',
    filters?: any
  ): Promise<Blob> {
    const { logs } = await this.getAuditLogs(businessId, filters);

    // Log the export action
    await this.logEvent({
      business_id: businessId,
      event_type: 'data_exported',
      event_description: `Audit logs exported in ${format} format`,
      metadata: { format, filters, record_count: logs.length },
    });

    // This would typically call an Edge Function for export generation
    const response = await fetch('/api/audit/export', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        logs,
        format,
        filters,
      }),
    });

    if (!response.ok) throw new Error('Export failed');
    return response.blob();
  }

  private async getRequestContext(): Promise<{
    ip_address?: string;
    user_agent?: string;
    request_id: string;
    session_id?: string;
  }> {
    return {
      ip_address: await this.getClientIP(),
      user_agent: navigator.userAgent,
      request_id: crypto.randomUUID(),
      session_id: this.getSessionId(),
    };
  }

  private async getClientIP(): Promise<string> {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch {
      return 'unknown';
    }
  }

  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('audit_session_id');
    if (!sessionId) {
      sessionId = crypto.randomUUID();
      sessionStorage.setItem('audit_session_id', sessionId);
    }
    return sessionId;
  }

  private getChangedFields(oldValues: any, newValues: any): string[] {
    const changed: string[] = [];
    
    for (const key in newValues) {
      if (oldValues[key] !== newValues[key]) {
        changed.push(key);
      }
    }
    
    return changed;
  }

  private async checkSecurityRules(eventData: any, requestContext: any): Promise<void> {
    // This would implement security rule checking logic
    // For example, detecting multiple failed login attempts, unusual access patterns, etc.
  }

  private async getLocationFromIP(ipAddress?: string): Promise<any> {
    if (!ipAddress || ipAddress === 'unknown') return null;
    
    try {
      // In a real implementation, you'd use a geolocation service
      return { country: 'Unknown', city: 'Unknown' };
    } catch {
      return null;
    }
  }

  private async sendSecurityAlert(securityEvent: SecurityEvent): Promise<void> {
    // Send security alert via email/Slack/etc.
    await fetch('/api/security/alert', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(securityEvent),
    });
  }

  private groupEventsByType(logs: AuditLog[]): Record<string, number> {
    return logs.reduce((acc, log) => {
      acc[log.event_type] = (acc[log.event_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  private groupEventsByUser(logs: AuditLog[]): Record<string, number> {
    return logs.reduce((acc, log) => {
      const userId = log.user_id || 'system';
      acc[userId] = (acc[userId] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  private async getSecurityEventsSummary(
    businessId: string,
    periodStart: string,
    periodEnd: string
  ): Promise<any> {
    const { events } = await this.getSecurityEvents(businessId, {
      date_from: periodStart,
      date_to: periodEnd,
    });

    return {
      total: events.length,
      by_severity: events.reduce((acc, event) => {
        acc[event.severity] = (acc[event.severity] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      by_status: events.reduce((acc, event) => {
        acc[event.status] = (acc[event.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
    };
  }

  private getDataAccessSummary(logs: AuditLog[]): any {
    const dataAccess = logs.filter(log => 
      log.event_type.includes('created') || 
      log.event_type.includes('updated') || 
      log.event_type.includes('deleted') ||
      log.event_type.includes('exported')
    );

    return {
      total_data_operations: dataAccess.length,
      by_operation: dataAccess.reduce((acc, log) => {
        const operation = log.event_type.split('_')[1] || 'unknown';
        acc[operation] = (acc[operation] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      by_resource: dataAccess.reduce((acc, log) => {
        const resource = log.resource_type || 'unknown';
        acc[resource] = (acc[resource] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
    };
  }
}
```

## Security Measures
- **Immutable Audit Logs**: Audit logs cannot be modified once created
- **Encrypted Storage**: Sensitive audit data encrypted at rest
- **Access Control**: Role-based access to audit logs and security events
- **Data Retention**: Configurable retention policies for compliance
- **Tamper Detection**: Integrity checks for audit log data
- **Secure Export**: Controlled and logged data export capabilities

## Integration Points
- **All Modules**: Audit logging integrated throughout the application
- **User Management**: User activity and session tracking
- **Business Management**: Business-scoped audit and security events
- **Email Service**: Security alert notifications
- **Reporting & Analytics**: Security and compliance reporting

## Error Handling & Validation
- **Graceful Failure**: Audit logging failures don't break main operations
- **Data Validation**: Comprehensive validation of audit log data
- **Retry Logic**: Automatic retry for failed audit log writes
- **Error Logging**: Separate error tracking for audit system issues
- **Backup Logging**: Alternative logging mechanisms for system failures

## Testing Strategy
- **Unit Tests**: Audit service methods and security rule logic
- **Integration Tests**: Audit log creation and retrieval
- **Security Tests**: Security rule effectiveness and threat detection
- **Compliance Tests**: Compliance report accuracy and completeness
- **Performance Tests**: High-volume audit log handling

## Implementation Tasks
1. **Database Schema Setup**
   - Create audit and security tables with partitioning
   - Implement RLS policies for secure access
   - Set up automated partition management

2. **Audit Service Development**
   - Build AuditService with comprehensive logging
   - Implement security event detection
   - Create compliance reporting system

3. **Security Monitoring**
   - Build security rule engine
   - Implement threat detection algorithms
   - Create security dashboard and alerts

4. **Compliance Features**
   - Build compliance report generation
   - Implement data retention policies
   - Create audit log export functionality

5. **Integration Implementation**
   - Integrate audit logging throughout application
   - Implement security event triggers
   - Create automated monitoring workflows

6. **Dashboard and Reporting**
   - Build audit log viewing interface
   - Create security event management dashboard
   - Implement compliance reporting interface

## Dependencies
- **All Modules**: Source of audit events and security monitoring
- **Email Service**: Security alert notifications
- **Document Management**: Compliance report storage
- **Background Jobs**: Automated data retention and cleanup

## Success Criteria
- ✅ All user actions are properly logged and auditable
- ✅ Security events are detected and responded to automatically
- ✅ Compliance reports can be generated accurately
- ✅ Audit logs are tamper-proof and immutable
- ✅ Data retention policies are enforced automatically
- ✅ Security dashboard provides real-time monitoring
- ✅ Export functionality works for compliance audits
- ✅ Performance is acceptable for high-volume logging
