# User Management

## Overview
This module handles user authentication, profile management, and role-based access control (RBAC) for the rInvoice application, leveraging Supabase's built-in authentication features.

## Core Functionalities
- User registration with email verification.
- OAuth2 social logins (e.g., Google, GitHub).
- JWT-based session management.
- User profile management (e.g., name, avatar).
- Role-Based Access Control (Admin, Manager, Accountant, Viewer).

## Technical Specifications

### Database Schema
This module relies on Supabase's `auth.users` table for authentication. A public `profiles` table is created in the `app` schema to store non-sensitive user data.

```mermaid
erDiagram
    auth.users ||--o{ app.profiles : "has one"
    auth.users {
        uuid id PK
        string email
        ...
    }
    app.profiles {
        uuid id PK
        uuid user_id FK
        string full_name
        string avatar_url
    }
```

```sql
-- Create the profiles table in the 'app' schema
CREATE TABLE app.profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID UNIQUE NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT,
    avatar_url TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Enable Row-Level Security (RLS)
ALTER TABLE app.profiles ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Users can view their own profile
CREATE POLICY "Users can view their own profile"
ON app.profiles FOR SELECT
USING (auth.uid() = user_id);

-- RLS Policy: Users can update their own profile
CREATE POLICY "Users can update their own profile"
ON app.profiles FOR UPDATE
USING (auth.uid() = user_id);
```

### API Endpoints
Authentication will be handled by Supabase's native endpoints, while profile management will be handled by our custom API.

| Method | Endpoint | Description | Handler |
|--------|----------|-------------|---------|
| POST   | /auth/v1/signup | User registration | Supabase |
| POST   | /auth/v1/token?grant_type=password | User login | Supabase |
| GET    | /api/users/me | Get current user's profile | Custom API |
| PATCH  | /api/users/me | Update current user's profile | Custom API |

### Security Measures
- Authentication is managed by Supabase Auth (GoTrue).
- JWTs are issued as secure, HttpOnly cookies.
- Row-Level Security (RLS) policies restrict data access.
- Rate limiting and account lockout policies are configured in Supabase.
- Audit trails for authentication events are available in Supabase logs.

## Integration Points
- **Business Management**: Associates users with businesses.
- **Customer Management**: Links users to notes they create.
- **Invoicing System**: Restricts invoice access based on user roles and business association.

## Error Handling & Validation
- Email format and uniqueness are handled by Supabase.
- Password policies (minimum length, complexity) are configured in Supabase.
- Standardized error responses are provided by Supabase APIs.

## Implementation Tasks
1.  Configure Supabase Auth settings (providers, email templates, security).
2.  Create the `app.profiles` table and its RLS policies.
3.  Implement frontend components for authentication and profile management.
4.  **Data Migration**:
    - Not applicable for a new application. If migrating from an existing system, a script would be needed to import users into `auth.users` and create corresponding `app.profiles` entries.

## Dependencies
- **Core Infrastructure**: Requires the Supabase project and database to be set up.
