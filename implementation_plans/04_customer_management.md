# Customer Management

## Overview
This module handles the management of customer data. Customers are scoped to a specific business, ensuring data isolation in a multi-tenant environment.

## Core Functionalities
- Manage customer profiles (personal or company details).
- Handle multiple addresses per customer.
- Configure customer-specific payment terms.
- Add internal notes to customer profiles.

## Technical Specifications

### Database Schema
Customers are linked to a business. Addresses and notes are linked to customers.

```mermaid
erDiagram
    app.businesses ||--|{ app.customers : "has"
    app.customers ||--o{ app.addresses : "has"
    app.customers ||--o{ app.notes : "has"
    auth.users ||--|{ app.notes : "creates"

    app.customers {
        uuid id PK
        uuid business_id FK
        text first_name
        text last_name
        citext email
        text phone
        text company_name
    }
    app.addresses {
        uuid id PK
        uuid customer_id FK
        text street
        text city
        text country
    }
    app.notes {
        uuid id PK
        uuid customer_id FK
        uuid created_by FK
        text content
    }
```

```sql
-- Create payment terms enum
CREATE TYPE app.payment_term_enum AS ENUM (
    'NET14', 'NET30', 'NET45', 'NET60', 'ON_ISSUE'
);

-- Create customers table, linked to a business
CREATE TABLE app.customers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    email citext,
    phone TEXT,
    company_name TEXT,
    display_name TEXT GENERATED ALWAYS AS (
        COALESCE(company_name, first_name || ' ' || last_name)
    ) STORED,
    currency TEXT NOT NULL DEFAULT 'USD',
    payment_terms app.payment_term_enum NOT NULL DEFAULT 'NET14',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CHECK (email IS NOT NULL OR phone IS NOT NULL)
);

-- Create addresses table
CREATE TABLE app.addresses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES app.customers(id) ON DELETE CASCADE,
    street TEXT NOT NULL,
    city TEXT NOT NULL,
    state TEXT,
    postal_code TEXT NOT NULL,
    country TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create notes table
CREATE TABLE app.notes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES app.customers(id) ON DELETE CASCADE,
    created_by UUID NOT NULL REFERENCES auth.users(id),
    content TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_customers_business_id ON app.customers(business_id);
CREATE INDEX idx_customers_email ON app.customers(email);
CREATE INDEX idx_addresses_customer_id ON app.addresses(customer_id);

-- Enable RLS
ALTER TABLE app.customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.notes ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Users can manage customers of businesses they belong to.
CREATE POLICY "Customer access for business members"
ON app.customers FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users WHERE user_id = auth.uid()
    )
);
-- (Similar policies to be created for addresses and notes based on customer access)
```

### API Endpoints
| Method | Endpoint | Description |
|--------|----------|-------------|
| POST   | /api/customers | Create a new customer |
| GET    | /api/customers | List customers for the user's active business |
| PATCH  | /api/customers/:id | Update a customer |
| DELETE | /api/customers/:id | Delete a customer |

### Security Measures
- RLS policies restrict customer data access to members of the corresponding business.
- Input validation is applied to all fields.
- Audit logs should be considered for sensitive operations like data deletion.

## Integration Points
- **Business Management**: Customers are owned by a business.
- **Invoicing System**: Invoices are created for customers.
- **User Management**: User information is used to track who created notes.

## Error Handling & Validation
- A contact method (email or phone) is required for each customer.
- Address structure must be validated.
- Check for duplicate customer entries within the same business.

## Implementation Tasks
1.  Create database migrations for the tables and RLS policies.
2.  Develop frontend components for customer management (forms, lists).
3.  Implement search and filtering functionality.

## Dependencies
- **Business Management**: Requires the business context to associate customers.
