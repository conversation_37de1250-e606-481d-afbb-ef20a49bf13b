# Product Catalog

## Overview
This module manages the products and services that can be billed on invoices. Products are scoped to a business to support multi-tenancy.

## Core Functionalities
- Product and service management (CRUD).
- Pricing and tax configuration.
- Inventory tracking (optional).
- Product categorization and search.

## Technical Specifications

### Database Schema
Products are linked to a business. Tax rules can be defined globally or per business.

```mermaid
erDiagram
    app.businesses ||--|{ app.products : "has"
    app.products }|--|| app.tax_rules : "uses"

    app.products {
        uuid id PK
        uuid business_id FK
        text name
        text description
        numeric price
        uuid tax_rule_id FK
        numeric stock_quantity
    }
    app.tax_rules {
        uuid id PK
        uuid business_id FK "Optional"
        text name
        numeric rate
    }
```

```sql
-- Create products table, linked to a business
CREATE TABLE app.products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    price NUMERIC(12, 2) NOT NULL CHECK (price >= 0),
    tax_rule_id UUID REFERENCES app.tax_rules(id), -- Can be nullable
    unit TEXT NOT NULL DEFAULT 'unit',
    stock_quantity NUMERIC(12, 3), -- Can be null if not tracking inventory
    category TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create tax_rules table
CREATE TABLE app.tax_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID REFERENCES app.businesses(id) ON DELETE CASCADE, -- Null for global rules
    name TEXT NOT NULL,
    rate NUMERIC(5, 4) NOT NULL CHECK (rate >= 0 AND rate <= 1),
    description TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (business_id, name)
);

-- Indexes for performance
CREATE INDEX idx_products_business_id ON app.products(business_id);
CREATE INDEX idx_tax_rules_business_id ON app.tax_rules(business_id);

-- Enable RLS
ALTER TABLE app.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.tax_rules ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Users can manage products of businesses they belong to.
CREATE POLICY "Product access for business members"
ON app.products FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users WHERE user_id = auth.uid()
    )
);

-- RLS Policy: Users can see global tax rules and rules for their businesses.
CREATE POLICY "Tax rule access for business members"
ON app.tax_rules FOR SELECT
USING (
    business_id IS NULL OR business_id IN (
        SELECT business_id FROM app.business_users WHERE user_id = auth.uid()
    )
);
```

### API Endpoints
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | /api/products | List products for the active business |
| POST   | /api/products | Create a new product |
| PATCH  | /api/products/:id | Update a product |
| DELETE | /api/products/:id | Delete a product |

### Security Measures
- RLS policies restrict product and tax rule access to authorized users.
- Input validation for prices (non-negative) and tax rates (0-100%).
- Business ownership is verified for all write operations.

## Integration Points
- **Business Management**: Products are owned by a business.
- **Invoicing System**: Products from the catalog can be added to invoices.

## Error Handling & Validation
- Validate that `price` is not negative.
- Ensure `tax_rule_id` (if provided) exists and is accessible to the business.
- Check for duplicate product names within the same business.
- Validate inventory quantities if they are being tracked.

## Implementation Tasks
1.  Create database migrations for `products` and `tax_rules` tables.
2.  Implement RLS policies for data access.
3.  Develop the frontend interface for managing products and tax rules.
4.  Implement search and categorization functionality.

## Dependencies
- **Business Management**: Requires the business context to scope products.
