# Invoicing System

## Overview
This module handles the end-to-end invoice workflow, from creation and itemization to PDF generation and delivery. Invoices are securely tied to a specific business.

## Core Functionalities
- Invoice creation with line items.
- Automatic, unique invoice numbering.
- PDF generation and email delivery.
- Support for discounts (item-level and overall).
- Immutable snapshots of customer and address data.
- Recurring invoice scheduling.

## Technical Specifications

### Database Schema
Invoices are linked to a business and a customer. Line items and address snapshots are linked to an invoice.

```mermaid
erDiagram
    app.businesses ||--|{ app.invoices : "issues"
    app.customers ||--|{ app.invoices : "receives"
    app.invoices ||--|{ app.invoice_items : "contains"
    app.invoices ||--|{ app.invoice_address_snapshots : "snapshots"

    app.invoices {
        uuid id PK
        uuid business_id FK
        uuid customer_id FK
        text invoice_number
        date issue_date
        date due_date
        invoice_status status
    }
    app.invoice_items {
        uuid id PK
        uuid invoice_id FK
        text description
        numeric quantity
        numeric unit_price
    }
```

```sql
-- Create invoice status enum
CREATE TYPE app.invoice_status AS ENUM (
    'draft', 'sent', 'paid', 'overdue', 'void'
);

-- Create invoices table
CREATE TABLE app.invoices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES app.customers(id) ON DELETE RESTRICT,
    invoice_number TEXT NOT NULL,
    issue_date DATE NOT NULL DEFAULT CURRENT_DATE,
    due_date DATE NOT NULL,
    status app.invoice_status NOT NULL DEFAULT 'draft',
    notes TEXT,
    currency TEXT NOT NULL DEFAULT 'USD',
    overall_discount_value NUMERIC(12, 2) DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (business_id, invoice_number)
);

-- Create invoice items table
CREATE TABLE app.invoice_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID NOT NULL REFERENCES app.invoices(id) ON DELETE CASCADE,
    product_id UUID REFERENCES app.products(id), -- Optional link to product catalog
    description TEXT NOT NULL,
    quantity NUMERIC(10, 3) NOT NULL CHECK (quantity > 0),
    unit_price NUMERIC(12, 2) NOT NULL CHECK (unit_price >= 0),
    discount_value NUMERIC(12, 2) DEFAULT 0,
    tax_rate NUMERIC(5, 4) DEFAULT 0,
    position INTEGER NOT NULL
);

-- Create immutable address snapshots
CREATE TABLE app.invoice_address_snapshots (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID NOT NULL REFERENCES app.invoices(id) ON DELETE CASCADE,
    address_type TEXT NOT NULL CHECK(address_type IN ('shipping', 'billing')),
    snapshot_data JSONB NOT NULL
);

-- Indexes
CREATE INDEX idx_invoices_customer_id ON app.invoices(customer_id);
CREATE INDEX idx_invoices_business_id ON app.invoices(business_id);

-- Enable RLS
ALTER TABLE app.invoices ENABLE ROW LEVEL SECURITY;
-- (Policies for items and snapshots will be based on invoice access)

-- RLS Policy: Users can manage invoices of businesses they belong to.
CREATE POLICY "Invoice access for business members"
ON app.invoices FOR ALL
USING (
    business_id IN (
        SELECT business_id FROM app.business_users WHERE user_id = auth.uid()
    )
);
```

### API Endpoints
| Method | Endpoint | Description |
|--------|----------|-------------|
| POST   | /api/invoices | Create a new draft invoice |
| GET    | /api/invoices | List invoices for the active business |
| GET    | /api/invoices/:id | Get a specific invoice |
| POST   | /api/invoices/:id/send | Send an invoice to a customer |

### Security Measures
- RLS policies ensure users can only access invoices belonging to their businesses.
- Input validation for all financial data (prices, quantities).
- Customer and address data is snapshotted upon invoice creation to ensure immutability.
- Invoice numbers must be unique per business.

## Integration Points
- **Business Management**: Invoices belong to a business.
- **Customer Management**: Invoices are issued to customers.
- **Product Catalog**: Invoice items can be populated from the product catalog.
- **Payment Workflows**: Payments are recorded against invoices, updating their status.

## Error Handling & Validation
- Validate that `quantity` and `unit_price` are not negative.
- Ensure `due_date` is on or after `issue_date`.
- Verify that the `customer_id` belongs to the same `business_id` as the invoice.
- Handle potential race conditions when generating unique invoice numbers.

## Implementation Tasks
1.  Create database migrations for the invoice-related tables and RLS policies.
2.  Implement a robust invoice number generation mechanism (e.g., using a dedicated sequence).
3.  Develop the service for creating immutable data snapshots.
4.  Build the frontend for creating and managing invoices with line items.
5.  Set up a service (e.g., a Supabase Edge Function) for PDF generation.
6.  Implement the recurring invoice scheduling logic.

## Dependencies
- **Customer Management**
- **Product Catalog**
- **Business Management**
