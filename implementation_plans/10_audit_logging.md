# Audit Logging

## Overview
This module provides a comprehensive and immutable audit trail for all significant actions performed within the application. It is designed to track changes to key resources like invoices, customers, and products for accountability and history tracking.

## Core Functionalities
- Automatically log create, update, and delete events for major resources.
- Log other significant business events (e.g., sending an invoice, confirming a payment).
- Provide a centralized view of all logs for a business.
- Allow viewing the history of a single, specific item (e.g., an invoice).

## Technical Specifications

### Database Schema
A single, polymorphic `audit_logs` table will be used to record all events.

```mermaid
erDiagram
    app.businesses ||--|{ app.audit_logs : "has"
    auth.users ||--|{ app.audit_logs : "performs action of"

    app.audit_logs {
        uuid id PK
        uuid business_id FK
        uuid user_id FK
        text action
        text resource_type
        uuid resource_id
        jsonb details
        timestamp created_at
    }
```

```sql
-- Create an ENUM for standardized action types
CREATE TYPE app.audit_action AS ENUM (
    'created', 'updated', 'deleted', 'sent', 'paid', 'viewed', 'reminded'
);

-- Create the single audit logs table
CREATE TABLE app.audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id),
    action app.audit_action NOT NULL,
    resource_type TEXT NOT NULL, -- e.g., 'invoice', 'customer'
    resource_id UUID NOT NULL, -- The ID of the item that was changed
    details JSONB, -- Stores the before/after state for 'updated' actions
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Indexes for efficient querying
CREATE INDEX idx_audit_logs_business_id ON app.audit_logs(business_id);
CREATE INDEX idx_audit_logs_resource ON app.audit_logs(resource_type, resource_id);

-- Enable RLS
ALTER TABLE app.audit_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Users can only see logs for businesses they are members of.
CREATE POLICY "Audit log access for business members"
ON app.audit_logs FOR SELECT
USING (
    business_id IN (
        SELECT business_id FROM app.business_users WHERE user_id = auth.uid()
    )
);
```

### Implementation via Database Triggers
The most reliable way to ensure every change is logged is by using PostgreSQL triggers. This is more robust than relying on the API to log events.

```sql
-- 1. Create a function to record the audit log
CREATE OR REPLACE FUNCTION app.log_change()
RETURNS TRIGGER AS $$
DECLARE
    audit_log_details JSONB;
BEGIN
    IF (TG_OP = 'UPDATE') THEN
        audit_log_details := jsonb_build_object('old', to_jsonb(OLD), 'new', to_jsonb(NEW));
    ELSIF (TG_OP = 'DELETE') THEN
        audit_log_details := jsonb_build_object('old', to_jsonb(OLD));
    ELSE
        audit_log_details := jsonb_build_object('new', to_jsonb(NEW));
    END IF;

    INSERT INTO app.audit_logs (business_id, user_id, action, resource_type, resource_id, details)
    VALUES (
        NEW.business_id, -- Assumes the table has a business_id column
        auth.uid(),
        lower(TG_OP)::app.audit_action,
        TG_TABLE_NAME,
        NEW.id,
        audit_log_details
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 2. Attach the trigger to a table (e.g., invoices)
CREATE TRIGGER invoices_audit_trigger
AFTER INSERT OR UPDATE OR DELETE ON app.invoices
FOR EACH ROW EXECUTE FUNCTION app.log_change();
```

### API Endpoints
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET    | /api/logs | Get all audit logs for the active business (with filters) |
| GET    | /api/invoices/:id/logs | Get the audit trail for a specific invoice |
| GET    | /api/customers/:id/logs | Get the audit trail for a specific customer |

## Integration Points
This module is deeply integrated at the database level with all other modules that handle mutable data, including:
- **Invoicing System**
- **Customer Management**
- **Product Catalog**
- **Business Management**

## Implementation Tasks
1.  Create the database migration for the `audit_logs` table and `audit_action` enum.
2.  Create the generic `log_change()` trigger function.
3.  Attach the trigger to all relevant tables (`invoices`, `customers`, `products`, etc.).
4.  Develop the API endpoints for querying the logs.
5.  Build the frontend components to display the audit trails.

## Dependencies
- **Core Infrastructure**: Foundational.
- Integrates with nearly all other modules.
