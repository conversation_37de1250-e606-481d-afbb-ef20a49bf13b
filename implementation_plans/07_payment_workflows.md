# Payment Workflows

## Overview
This module handles payment tracking for invoices, automated reminders for overdue payments, and payment reconciliation.

## Core Functionalities
- Record payments against invoices.
- Track payment status (e.g., pending, confirmed, failed).
- Automated payment reminder system.
- Generate and deliver payment receipts.

## Technical Specifications

### Database Schema
Payments are linked to a single invoice. Reminders are also linked to an invoice.

```mermaid
erDiagram
    app.invoices ||--|{ app.payments : "has"
    app.invoices ||--|{ app.payment_reminders : "has"

    app.payments {
        uuid id PK
        uuid invoice_id FK
        numeric amount
        date date
        text method
        text status
    }
    app.payment_reminders {
        uuid id PK
        uuid invoice_id FK
        text reminder_type
        timestamp sent_at
    }
```

```sql
-- Create payments table
CREATE TABLE app.payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID NOT NULL REFERENCES app.invoices(id) ON DELETE CASCADE,
    amount NUMERIC(12, 2) NOT NULL CHECK (amount > 0),
    method TEXT NOT NULL CHECK (method IN ('cash', 'credit_card', 'bank_transfer', 'check', 'online')),
    reference TEXT,
    date DATE NOT NULL DEFAULT CURRENT_DATE,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'failed', 'refunded')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create payment_reminders table
CREATE TABLE app.payment_reminders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID NOT NULL REFERENCES app.invoices(id) ON DELETE CASCADE,
    reminder_type TEXT NOT NULL CHECK (reminder_type IN ('due_soon', 'overdue', 'final_notice')),
    sent_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_payments_invoice_id ON app.payments(invoice_id);
CREATE INDEX idx_reminders_invoice_id ON app.payment_reminders(invoice_id);

-- Enable RLS
ALTER TABLE app.payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.payment_reminders ENABLE ROW LEVEL SECURITY;

-- RLS policies will be based on access to the parent invoice.
CREATE POLICY "Payment access for invoice members"
ON app.payments FOR ALL
USING (
    (SELECT business_id FROM app.invoices WHERE id = invoice_id) IN (
        SELECT business_id FROM app.business_users WHERE user_id = auth.uid()
    )
);
-- (A similar policy to be created for payment_reminders)
```

### API Endpoints
| Method | Endpoint | Description |
|--------|----------|-------------|
| POST   | /api/invoices/:invoiceId/payments | Record a new payment for an invoice |
| GET    | /api/invoices/:invoiceId/payments | List payments for an invoice |
| POST   | /api/invoices/:invoiceId/reminders | Send a payment reminder |

### Security Measures
- RLS policies ensure payments can only be managed by users with access to the parent invoice.
- Payment amounts are validated against the outstanding invoice balance.
- Audit logging should be in place for all payment-related activities.

## Integration Points
- **Invoicing System**: Payments update the status of invoices (e.g., to 'paid' or 'partial').
- **Notification System**: Sends alerts for successful payments or failures.
- **Email System**: Delivers payment receipts and reminders.

## Error Handling & Validation
- Validate that the payment `amount` does not exceed the invoice balance.
- Verify that the `invoice_id` exists and is in a state that can accept payments.
- Handle failures from payment gateways if integrated.

## Implementation Tasks
1.  Create database migrations for `payments` and `payment_reminders` tables.
2.  Implement RLS policies for data access.
3.  Develop a Supabase Edge Function (or other cron job) to schedule and send reminders.
4.  Build the frontend interface for recording and viewing payments.
5.  Implement the logic for generating payment receipts.

## Dependencies
- **Invoicing System**: Payments are fundamentally linked to invoices.
