# Business Management

## Overview
This module handles the relationship between users and businesses, allowing a user to own or be a member of multiple businesses.

## Core Functionalities
- Create and manage multiple businesses.
- Associate users with businesses and define their roles.
- Manage business details (name, logo, address, tax info).
- Default 'Personal' business created for each new user.

## Technical Specifications

### Database Schema
A `businesses` table stores business information, and a `business_users` table links users to businesses with specific roles.

```mermaid
erDiagram
    app.profiles ||--|{ app.businesses : "can own"
    app.businesses ||--|{ app.business_users : "has many"
    app.profiles ||--|{ app.business_users : "is a member of"

    app.businesses {
        uuid id PK
        uuid owner_id FK "to app.profiles"
        text name
        text logo_url
        jsonb address
        text tax_id
        jsonb bank_info
    }

    app.business_users {
        uuid business_id FK
        uuid user_id FK "to auth.users"
        text role
    }
```

```sql
-- Create businesses table
CREATE TABLE app.businesses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    owner_id UUID NOT NULL REFERENCES app.profiles(id) ON DELETE CASCADE,
    name TEXT NOT NULL DEFAULT 'Personal',
    logo_url TEXT,
    address JSONB,
    tax_id TEXT,
    bank_info JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create a junction table for business members
CREATE TABLE app.business_users (
    business_id UUID NOT NULL REFERENCES app.businesses(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    role TEXT NOT NULL DEFAULT 'viewer', -- e.g., admin, manager, viewer
    PRIMARY KEY (business_id, user_id)
);

-- Indexes for performance
CREATE INDEX idx_businesses_owner_id ON app.businesses(owner_id);
CREATE INDEX idx_business_users_user_id ON app.business_users(user_id);

-- Enable Row-Level Security
ALTER TABLE app.businesses ENABLE ROW LEVEL SECURITY;
ALTER TABLE app.business_users ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Users can see businesses they are members of.
CREATE POLICY "Users can see their own businesses"
ON app.businesses FOR SELECT
USING (
    id IN (
        SELECT business_id FROM app.business_users WHERE user_id = auth.uid()
    )
);

-- RLS Policy: Business owners can update their business.
CREATE POLICY "Owners can update their business"
ON app.businesses FOR UPDATE
USING (owner_id = (SELECT id FROM app.profiles WHERE user_id = auth.uid()));
```

### API Endpoints
| Method | Endpoint | Description |
|--------|----------|-------------|
| POST   | /api/businesses | Create a new business |
| GET    | /api/businesses | List businesses for the current user |
| PATCH  | /api/businesses/:id | Update business details |
| POST   | /api/businesses/:id/logo | Upload business logo |

### Security Measures
- RLS policies ensure users can only access businesses they are members of.
- Specific roles (e.g., 'owner', 'admin') are required for modification actions, enforced by RLS or application logic.
- Input validation for all fields (e.g., address structure, tax ID format).
- Secure file uploads for logos using Supabase Storage, with policies restricting access.

## Integration Points
- **User Management**: Links users (via `auth.users` and `app.profiles`) to businesses.
- **Invoicing System**: Invoices are created under a specific business.
- **Customer Management**: Customers are associated with a specific business.

## Error Handling & Validation
- Validate address structure if provided: `{street, city, postal_code, country}`.
- Verify user permissions before allowing create, update, or delete operations.
- Validate logo file types (e.g., PNG, JPG) and size (<5MB) during upload.
- Check for duplicate business names for the same owner.

## Implementation Tasks
1.  Create the `app.businesses` and `app.business_users` table migrations.
2.  Implement RLS policies for data access.
3.  Develop a serverless function to create a default 'Personal' business for new users.
4.  Build frontend components for creating and managing businesses.
5.  Implement logo upload functionality using Supabase Storage.

## Dependencies
- **User Management**: Depends on user and profile information.
